{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@img/*": ["./src/assets/img/*"], "@c/*": ["./src/components/*"]}, "target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "jsx": "preserve", "lib": ["DOM", "ES2020", "DOM.Iterable"], "allowJs": true, "checkJs": false, "strict": false, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "types": ["vite/client", "element-plus/global"]}, "include": ["src/**/*", "src/**/*.vue", "src/**/*.js", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.jsx", "src/**/*.tsx", "vite.config.js"], "exclude": ["node_modules", "dist", "public"]}