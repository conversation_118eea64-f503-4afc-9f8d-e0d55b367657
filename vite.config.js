import path from 'path'
import { fileURLToPath } from 'url'

import vue from '@vitejs/plugin-vue'
import pxToViewport from 'postcss-px-to-viewport'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import viteCompression from 'vite-plugin-compression'

const filename = fileURLToPath(import.meta.url) // 这里不能声明__filename,因为已经有内部的__filename了，重复声明会报错
const _dirname = path.dirname(filename)

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
	return {
		base: './',
		plugins: [
			vue(),
			AutoImport({
				resolvers: [ElementPlusResolver()],
				imports: ['vue', 'vue-router'],
				dts: 'src/auto-import.d.ts'
			}),
			Components({
				resolvers: [ElementPlusResolver()]
			}),
			viteCompression({
				verbose: true, // 是否在控制台输出压缩结果
				disable: false, // 是否禁用，相当于开关在这里
				threshold: 1024000 * 0.1, // 对大于 100K 的文件进行压缩
				algorithm: 'gzip', // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
				ext: '.gz' // 文件后缀
			})
		],
		resolve: {
			alias: {
				'@': path.resolve(_dirname, './src'),
				'@img': path.resolve(_dirname, './src/assets/img'),
				'@c': path.resolve(_dirname, './src/components')
			}
		},
		css: {
			postcss: {
				plugins: [
					pxToViewport({
						unitToConvert: 'px', // 需要转换的单位
						viewportWidth: 9600, // 设计稿宽度
						unitPrecision: 5, // 保留小数位数
						propList: ['*'], // 所有属性都转换
						viewportUnit: 'vw', // 转换为 vw
						fontViewportUnit: 'vw', // 字体也用 vw
						selectorBlackList: [], // 忽略的类名
						minPixelValue: 1, // 小于或等于 1px 不转换
						mediaQuery: false, // 不处理媒体查询
						exclude: [] // 排除第三方依赖
					})
				]
			},
			preprocessorOptions: {
				scss: {
					additionalData: `@use "@/assets/style/variables.scss" as *;`,
					api: 'modern-compiler',
					javascriptEnabled: true
				}
			}
		},
		server: {
			// 是否开启 https
			https: false,
			// 端口号
			port: 9208,
			// 监听所有地址
			host: '0.0.0.0',
			// 服务启动时是否自动打开浏览器
			open: true,
			// 允许跨域
			cors: true,
			// 自定义代理规则
			proxy: {}
		},
		build: {
			// 设置最终构建的浏览器兼容目标
			// target: 'es2015',
			// 构建后是否生成 source map 文件
			sourcemap: false,
			//  chunk 大小警告的限制（以 kbs 为单位）
			chunkSizeWarningLimit: 2000,
			// 启用/禁用 gzip 压缩大小报告
			reportCompressedSize: false,
			minify: 'terser',
			terserOptions: {
				compress: {
					drop_console: true,
					drop_debugger: true
				}
			},
			rollupOptions: {
				maxParallelFileOps: 10, // 限制并发文件操作数
				output: {
					// 最小化拆分包
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id
								.toString()
								.split('node_modules/')[1]
								.split('/')[0]
								.toString()
						}
					}
				}
			}
		}
	}
})
