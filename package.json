{"name": "control-center-bigscreen", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "preview": "vite preview", "prettier": "prettier --write .", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-plus": "^2.9.10", "pinia": "^3.0.2", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "eslint": "^8.38.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.18.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.11.0", "husky": "^8.0.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.8.7", "sass": "^1.89.0", "terser": "^5.39.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^9.4.3"}}