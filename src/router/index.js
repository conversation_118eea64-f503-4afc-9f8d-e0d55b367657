import { createRouter, createWebHashHistory } from 'vue-router'

import { menuConfig } from './menu'

export const routes = [
	{
		path: '/',
		component: () => import('@/views/layout/index.vue'),
		redirect: '/overall-overview',
		children: menuConfig
	}
]

const router = createRouter({
	history: createWebHashHistory(),
	routes,
	scrollBehavior() {
		return {
			el: '#app',
			top: 0,
			behavior: 'smooth'
		}
	}
})
export default router
