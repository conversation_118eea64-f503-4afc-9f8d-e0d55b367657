export const menuConfig = [
	{
		path: '/production-sales',
		name: 'productionSales',
		meta: {
			title: '产量销量',
			icon: ''
		},
		component: () => import('@/views/productionSales/index.vue')
	},
	{
		path: '/quality-monitoring',
		name: 'qualityMonitoring',
		meta: {
			title: '质量监控',
			icon: ''
		},
		component: () => import('@/views/qualityMonitoring/index.vue')
	},
	{
		path: '/workplace-safety',
		name: 'workplaceSafety',
		meta: {
			title: '安全生产',
			icon: ''
		},
		component: () => import('@/views/workplaceSafety/index.vue')
	},
	{
		path: '/equipment-management',
		name: 'equipmentManagement',
		meta: {
			title: '设备管理',
			icon: ''
		},
		component: () => import('@/views/equipmentManagement/index.vue')
	},
	{
		path: '/overall-overview',
		name: 'overallOverview',
		meta: {
			title: '总体概况',
			icon: ''
		},
		component: () => import('@/views/overallOverview/index.vue')
	},
	{
		path: '/energy-management',
		name: 'energyManagement',
		meta: {
			title: '动能管理',
			icon: ''
		},
		component: () => import('@/views/energyManagement/index.vue')
	},
	{
		path: '/logistics-management',
		name: 'logisticsManagement',
		meta: {
			title: '物流管理',
			icon: ''
		},
		component: () => import('@/views/logisticsManagement/index.vue')
	},
	{
		path: '/cost-management',
		name: 'costManagement',
		meta: {
			title: '成本管理',
			icon: ''
		},
		component: () => import('@/views/costManagement/index.vue')
	},
	{
		path: '/collaborative-office',
		name: 'collaborativeOffice',
		meta: {
			title: '协同办公',
			icon: ''
		},
		component: () => import('@/views/collaborativeOffice/index.vue')
	}
]
