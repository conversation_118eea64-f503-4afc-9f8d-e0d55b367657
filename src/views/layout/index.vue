<template>
	<div class="layout-container">
		<div class="layout-border top">
			<div class="title">虹阳显示智造指挥系统</div>
		</div>
		<div class="layout-border left"></div>
		<div class="layout-border right"></div>
		<div class="header-info">
			<div>
				<div class="item">
					<img src="@/assets/img/icon-time.png" alt="" />
					<span>{{ data.time }}</span
					>&nbsp; <span>{{ data.date }}</span
					>&nbsp;
					<span>{{ data.weekday }}</span>
				</div>
				<div class="item">
					<img src="@/assets/img/icon-admin.png" alt="" />
					<span>值班人员:</span>
					<span>{{ data.admin }}</span>
				</div>
			</div>
			<div>
				<div class="item">
					<img src="@/assets/img/icon-temperature.png" alt="" />
					<span>实时温度:</span>
					<span>{{ data.temperature }}℃</span>
				</div>
				<div class="item">
					<img src="@/assets/img/icon-humidity.png" alt="" />
					<span>湿度:</span>
					<span>{{ data.humidity }}%</span>
				</div>
				<div class="item">
					<img src="@/assets/img/icon-safe.png" alt="" />
					<span>安全环保天数:</span>
					<span class="safe-days">{{ data.safeDays }}</span>
					<span>天</span>
				</div>
			</div>
		</div>
		<router-view></router-view>
	</div>
</template>

<script setup>
// import {  menuConfig } from '@/router/menu'
import { dateFormat } from '@/utils/public.js'

const data = reactive({
	time: '',
	date: '',
	weekday: '',
	admin: `张某某 李某某`,
	temperature: 22,
	humidity: 15,
	safeDays: 108
})

const timer = shallowRef(null)

const initTimer = () => {
	const current = new Date()
	data.time = dateFormat('HH:mm:ss', current)
	data.date = dateFormat('YYYY/MM/DD', current)
	const dayOfWeek = current.getDay()
	const weekdays = [
		'星期日',
		'星期一',
		'星期二',
		'星期三',
		'星期四',
		'星期五',
		'星期六'
	]
	data.weekday = weekdays[dayOfWeek]
	timer.value = setInterval(() => {
		data.time = dateFormat('HH:mm:ss', new Date())
	}, 1000)
}

onMounted(() => {
	initTimer()
})
onBeforeUnmount(() => {
	if (timer.value) {
		clearInterval(timer.value)
		timer.value = null
	}
})
</script>

<style scoped lang="scss">
@mixin setBackground($url) {
	background: url($url) no-repeat;
	background-size: 100% 100%;
}
.layout-container {
	width: 100%;
	height: 100%;
	position: relative;
	.layout-border {
		position: absolute;
		pointer-events: none;
		&.top {
			width: 6700.7px;
			height: 138.7px;
			@include setBackground('@/assets/img/layout-border-top.png');
			left: 50%;
			transform: translateX(-50%);
			text-align: center;

			.title {
				font-family: YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 58px;
				line-height: 110px;
				color: #ffffff;
				text-shadow: 3px 5px 0 rgba(17, 20, 22, 0.42);
				background: linear-gradient(
					180deg,
					rgba(255, 255, 255, 1) 0%,
					rgba(255, 255, 255, 0.9) 50%,
					rgba(255, 255, 255, 0.4) 95%,
					rgba(255, 255, 255, 0.35) 100%
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
		}
		&.left,
		&.right {
			width: 338.3px;
			height: 1037.3px;
			top: 50%;
			transform: translateY(-50%);
		}
		&.left {
			left: 26px;
			@include setBackground('@/assets/img/layout-border-left.png');
		}
		&.right {
			right: 26px;
			@include setBackground('@/assets/img/layout-border-right.png');
		}
	}
	.header-info {
		position: absolute;
		width: 2100px;
		left: 50%;
		top: 90px;
		transform: translateX(-50%);
		pointer-events: none;
		display: flex;
		align-items: center;
		justify-content: space-between;
		> div {
			display: flex;
			align-items: center;
			gap: 23px;
			.item {
				display: flex;
				align-items: center;
				img {
					width: 48.7px;
					height: 48.7px;
				}
				span {
					font-family: Microsoft YaHei;
					font-weight: 400;
					font-size: 20px;
					color: rgba(202, 235, 247, 0.85);
					line-height: 35px;
				}
				.safe-days {
					font-family: Microsoft YaHei;
					font-weight: bold;
					font-size: 30px;
					color: #077cff;
					line-height: 35px;
					padding: 0 4px;
				}
			}
		}
	}
}
</style>
