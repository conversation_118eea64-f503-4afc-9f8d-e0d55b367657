const currentYear = new Date().getFullYear()
const currentMonth = new Date().getMonth() + 1

// 生成指定范围内的随机整数
const getRandomInt = (min, max) => {
	return Math.floor(Math.random() * (max - min + 1)) + min
}

// 获取指定时间段内的x轴数据
const getXData = (type = 'year') => {
	let unit = '月'
	let xDataLen = 12
	if (type === 'month') {
		unit = ''
		xDataLen = new Date(
			new Date().getFullYear(),
			new Date().getMonth() + 1,
			0
		).getDate()
	}

	return Array.from({ length: xDataLen }, (v, k) => {
		return `${k + 1}${unit}`
	})
}

// 获取指定时间段内的series数据
const getSeriesData = (max, min, type = 'year') => {
	let seriesDataLen = currentMonth
	if (type === 'month') {
		seriesDataLen = new Date().getDate()
	}

	return Array.from({ length: seriesDataLen }, (v, k) => {
		return getRandomInt(max, min)
	})
}

// 设置柱状图itemStyle
const setBarItemColor = (r, g, b) => {
	return {
		type: 'linear',
		x: 0,
		y: 0,
		x2: 0,
		y2: 1,
		colorStops: [
			{
				offset: 0,
				color: `rgb(${r}, ${g}, ${b})`
			},
			{
				offset: 1,
				color: `rgba(${r}, ${g}, ${b}, 0.05)`
			}
		]
	}
}

export const barAndLineChart = {
	// 产品年生产/销售
	chartData1: {
		chartData: {
			xData: [currentYear - 1, currentYear],
			seriesData: [
				{
					name: '生产',
					data: [180, 210],
					type: 'bar',
					barWidth: 17,
					itemStyle: {
						borderRadius: [4, 4, 0, 0],
						color: setBarItemColor(0, 234, 255)
					},
					animationDelay(idx) {
						return idx * 300
					}
				},
				{
					name: '销售',
					data: [180, 200],
					type: 'bar',
					barWidth: 17,
					itemStyle: {
						borderRadius: [4, 4, 0, 0],
						color: setBarItemColor(40, 70, 255)
					},
					animationDelay(idx) {
						return idx * 300 + 150
					}
				}
			]
		},
		yAxisName: ['万片'],
		unit: '万片',
		colorList: ['#00EAFF', '#2846FF']
	},

	// 产品月计划/生产/销售
	chartData2: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '计划生产',
					data: getSeriesData(20, 40),
					type: 'line',
					smooth: false
				},
				{
					name: '计划销售',
					data: getSeriesData(20, 40),
					type: 'line',
					smooth: false
				},
				{
					name: '生产',
					data: getSeriesData(20, 40),
					type: 'bar',
					barWidth: 6,
					itemStyle: {
						borderRadius: [3, 3, 0, 0],
						color: setBarItemColor(0, 234, 255)
					}
				},
				{
					name: '销售',
					data: getSeriesData(20, 40),
					type: 'bar',
					barWidth: 6,
					itemStyle: {
						borderRadius: [3, 3, 0, 0],
						color: setBarItemColor(40, 70, 255)
					}
				}
			]
		},
		yAxisName: ['万片'],
		unit: '万片',
		colorList: ['#16FA91', '#AE57FF', '#00EAFF', '#2846FF']
	},

	// 产品日计划/生产
	chartData3: {
		chartData: {
			xData: getXData('month'),
			seriesData: [
				{
					name: '计划生产',
					data: getSeriesData(1, 2, 'month'),
					type: 'line',
					smooth: false
				},
				{
					name: '实际生产',
					data: getSeriesData(1, 2, 'month'),
					type: 'bar',
					barWidth: 6,
					itemStyle: {
						borderRadius: [3, 3, 0, 0],
						color: setBarItemColor(0, 234, 255)
					},
					animationDelay(idx) {
						return idx * 300
					}
				}
			]
		},
		yAxisName: ['万片'],
		unit: '万片',
		colorList: ['#16FA91', '#00EAFF']
	},

	// 设备稼动率
	chartData4: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '稼动率目标',
					data: getSeriesData(30, 90),
					type: 'line',
					smooth: false,
					symbol: 'circle',
					symbolSize: 6
				},
				{
					name: '实际稼动率',
					data: getSeriesData(30, 90),
					type: 'line',
					smooth: false,
					symbol: 'circle',
					symbolSize: 6
				}
			]
		},
		yAxisName: ['%'],
		unit: '%',
		colorList: ['#34FDF6', '#3471FD'],
		showSplitArea: true,
		showYAxisLine: true
	},

	// 月综合良率
	chartData5: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '成型不良',
					data: getSeriesData(10, 20),
					type: 'bar',
					stack: 'total',
					barWidth: 31,
					itemStyle: {
						color: '#FF830A'
					}
				},
				{
					name: '研磨不良',
					data: getSeriesData(20, 30),
					type: 'bar',
					stack: 'total',
					barWidth: 31,
					itemStyle: {
						color: '#1679FF'
					}
				},
				{
					name: '溶解不良',
					data: getSeriesData(30, 50),
					type: 'bar',
					stack: 'total',
					barWidth: 31,
					itemStyle: {
						color: setBarItemColor(222, 256, 66)
					}
				},
				{
					name: '指标线',
					type: 'line',
					data: [],
					markLine: {
						data: [
							{
								yAxis: 90,
								label: {
									color: '#FF3535'
								}
							}
						],
						symbol: 'none'
					}
				},
				{
					name: '良率',
					data: getSeriesData(20, 70),
					type: 'line',
					smooth: false
				}
			]
		},
		yAxisName: ['%'],
		unit: '%',
		colorList: ['#FF830A', '#1679FF', '#DEFF42', '#FF3535', '#2AFF7F']
	},

	// 单板缺陷
	chartData6: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '结石',
					data: getSeriesData(10, 20),
					type: 'bar',
					stack: 'total',
					barWidth: 12,
					itemStyle: {
						color: '#4CE0FB',
						borderColor: 'transparent',
						borderWidth: 2
					}
				},
				{
					name: '钣金',
					data: getSeriesData(20, 30),
					type: 'bar',
					stack: 'total',
					barWidth: 12,
					itemStyle: {
						color: '#398BF1',
						borderColor: 'transparent',
						borderWidth: 2
					}
				},
				{
					name: '气泡',
					data: getSeriesData(30, 50),
					type: 'bar',
					stack: 'total',
					barWidth: 12,
					itemStyle: {
						color: setBarItemColor(111, 80, 243),
						borderColor: 'transparent',
						borderWidth: 2
					}
				},
				{
					name: '目标',
					data: getSeriesData(20, 70),
					type: 'line',
					smooth: false
				}
			]
		},
		yAxisName: ['%'],
		unit: '%',
		colorList: ['#4CE0FB', '#398BF1', '#6F50F3', '#60F478']
	},

	// EHR系统-资产概况
	chartData7: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '原值',
					data: getSeriesData(60, 10),
					type: 'bar',
					barWidth: 5,
					itemStyle: {
						color: setBarItemColor(0, 234, 255)
					},
					animationDelay(idx) {
						return idx * 300
					}
				},
				{
					name: '折旧',
					data: getSeriesData(20, 40),
					type: 'bar',
					barWidth: 5,
					itemStyle: {
						color: setBarItemColor(28, 115, 245)
					},
					animationDelay(idx) {
						return idx * 300 + 150
					}
				},
				{
					name: '净值',
					data: getSeriesData(40, 90),
					type: 'bar',
					barWidth: 5,
					itemStyle: {
						color: setBarItemColor(255, 217, 40)
					},
					animationDelay(idx) {
						return idx * 300 + 300
					}
				},
				{
					name: '新增',
					data: getSeriesData(10, 20),
					type: 'bar',
					barWidth: 5,
					itemStyle: {
						color: setBarItemColor(255, 111, 40)
					},
					animationDelay(idx) {
						return idx * 300 + 450
					}
				}
			]
		},
		yAxisName: ['万元'],
		unit: '万元',
		colorList: ['#00EAFF', '#1C73F5', '#FFD928', '#FF6F28']
	},

	// 智慧能源-控碳路径
	chartData8: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: '节碳量',
					data: getSeriesData(280, 420),
					type: 'line',
					smooth: false
				},
				{
					name: '减碳量',
					data: getSeriesData(120, 320),
					type: 'line',
					smooth: false
				}
			]
		},
		yAxisName: ['t'],
		unit: 't',
		colorList: ['#34FDF6', '#1557FF'],
		areaStyle: true
	},

	// 人脸识别系统概况
	chartData9: {
		chartData: {
			xData: ['点位1', '点位2', '点位3', '点位4', '点位5', '点位6'],
			seriesData: [
				{
					name: '抓拍次数',
					data: [58, 64, 42, 79, 45, 58],
					type: 'bar',
					barWidth: 10,
					itemStyle: {
						color: setBarItemColor(0, 234, 255)
					},
					animationDelay(idx) {
						return idx * 300
					}
				},
				{
					name: '识别次数',
					data: [58, 64, 42, 79, 45, 58],
					type: 'bar',
					barWidth: 10,
					itemStyle: {
						color: setBarItemColor(28, 115, 245)
					},
					animationDelay(idx) {
						return idx * 300 + 150
					}
				},
				{
					name: '报警次数',
					data: [9, 22, 22, 34, 17, 19],
					type: 'bar',
					barWidth: 10,
					itemStyle: {
						color: setBarItemColor(255, 217, 40)
					},
					animationDelay(idx) {
						return idx * 300 + 300
					}
				},
				{
					name: '处理次数',
					data: [9, 22, 22, 34, 17, 19],
					type: 'bar',
					barWidth: 10,
					itemStyle: {
						color: setBarItemColor(255, 111, 40)
					},
					animationDelay(idx) {
						return idx * 300 + 450
					}
				}
			]
		},
		yAxisName: ['次'],
		unit: '次',
		colorList: ['#00EAFF', '#1C73F5', '#FFD928', '#FF6F28']
	},

	// 入侵防御防护状态
	chartData10: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: 'Email',
					data: getSeriesData(120, 220),
					type: 'line',
					smooth: false
				},
				{
					name: 'FTP',
					data: getSeriesData(80, 260),
					type: 'line',
					smooth: false
				},
				{
					name: 'Web',
					data: getSeriesData(140, 220),
					type: 'line',
					smooth: false
				},
				{
					name: 'UDP',
					data: getSeriesData(200, 300),
					type: 'line',
					smooth: false
				},
				{
					name: 'Other',
					data: getSeriesData(280, 420),
					type: 'line',
					smooth: false
				}
			]
		},
		yAxisName: ['次'],
		unit: '次',
		colorList: ['#1CE3F5', '#3657FC', '#6048F7', '#FCC236', '#EE8156'],
		showSplitArea: true,
		showYAxisLine: true
	},

	// 邮件系统概览
	chartData11: {
		chartData: {
			xData: getXData(),
			seriesData: [
				{
					name: 'Pop',
					data: getSeriesData(120, 220),
					type: 'line',
					smooth: false,
					animationDelay(idx) {
						return idx * 300
					}
				},
				{
					name: 'Imap',
					data: getSeriesData(80, 260),
					type: 'line',
					smooth: false
				},
				{
					name: 'Web',
					data: getSeriesData(140, 220),
					type: 'line',
					smooth: false
				},
				{
					name: 'Smtp',
					data: getSeriesData(200, 300),
					type: 'line',
					smooth: false
				},
				{
					name: 'Lunkr',
					data: getSeriesData(280, 420),
					type: 'line',
					smooth: false
				}
			]
		},
		yAxisName: ['次数'],
		unit: '次',
		colorList: ['#1CE3F5', '#3657FC', '#36FCAA', '#FCC236', '#EE8156'],
		showSplitArea: true,
		showYAxisLine: true
	}
}
