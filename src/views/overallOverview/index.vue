<template>
	<div class="page-container">
		<!--3d建模-->
		<iframe name="modeling" :src="frameUrl"></iframe>
		<!--中间区域指示箭头-->
		<div class="middle-area"></div>
		<!--左侧-->
		<!--<LeftBox />-->
		<!--右侧-->
		<RightBox />
	</div>
</template>

<script setup>
import LeftBox from './LeftBox.vue'
import RightBox from './RightBox.vue'

const modules = import.meta.glob('../../assets/img/blockTitle/*.png')
const imgMap = ref({})
async function loadModules() {
	for (const path in modules) {
		if (Object.hasOwn(modules, path)) {
			// eslint-disable-next-line no-await-in-loop
			const module = await modules[path]()
			const key = path.replace(/[^\d]/g, '')
			const val = path.replace('../../', '/src/')
			imgMap.value[key] = val
		}
	}
}
loadModules()

const frameUrl = ''

provide('imgMap', imgMap)
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100%;
	iframe {
		width: 100%;
		height: 100%;
	}
	.left-wrapper,
	.right-wrapper {
		width: 3470.7px;
		height: calc(100% - 180px);
		top: 96px;
		position: absolute;
		z-index: 1;
	}
	.left-wrapper {
		left: 26px;
	}
	.right-wrapper {
		left: 26px;
	}
	.middle-area {
		width: 2528px;
		height: 794.3px;
		position: absolute;
		pointer-events: none;
		left: 50%;
		top: 148.7px;
		transform: translateX(-50%);
		background: url('@/assets/img/center-arrow.png') no-repeat;
		background-size: 100% 100%;
	}
}
</style>
