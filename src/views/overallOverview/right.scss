@mixin setBackground($url) {
	background: url($url) no-repeat;
	background-size: 100% 100%;
}
.right-wrapper {
	display: flex;
	.row {
		display: flex;
		height: 100%;
	}
	.row-1 {
		width: 759.7px;
		flex-direction: column;
		justify-content: space-between;
		margin-right: 36px;
		> div {
			&:nth-of-type(1) {
				height: 327.7px;
				margin-bottom: 33px;
			}
			&:nth-of-type(2) {
				height: 292px;
				margin-bottom: 17px;
			}
			&:nth-of-type(3) {
				height: 232.4px;
			}
		}
	}
	.row-2 {
		width: 808px;
		margin-right: 30px;
		flex-wrap: wrap;
		> div {
			&:nth-of-type(1) {
				width: 100%;
				height: 217px;
				margin-bottom: 29px;
			}
			&:nth-of-type(2),
			&:nth-of-type(3) {
				height: 315px;
				margin-bottom: 41px;
			}
			&:nth-of-type(2) {
				width: 446.7px;
				margin-right: 24px;
			}
			&:nth-of-type(3) {
				width: calc(100% - 470.7px);
			}
			&:nth-of-type(4),
			&:nth-of-type(5) {
				height: 294px;
			}
			&:nth-of-type(4) {
				width: 373.7px;
				margin-right: 25px;
			}
			&:nth-of-type(5) {
				width: calc(100% - 398.7px);
			}
		}
	}
	.row-3 {
		width: 878.7px;
		margin-right: 37px;
		flex-wrap: wrap;
		> div {
			border: 1px solid red;
			&:nth-of-type(1) {
				width: 100%;
				height: 223.5px;
				margin-bottom: 24px;
			}
			&:nth-of-type(2),
			&:nth-of-type(3) {
				height: 314px;
				margin-bottom: 40px;
			}
			&:nth-of-type(2),
			&:nth-of-type(4) {
				width: 447.7px;
				margin-right: 17px;
			}
			&:nth-of-type(3),
			&:nth-of-type(5) {
				width: calc(100% - 464.7px);
			}
			&:nth-of-type(4),
			&:nth-of-type(5) {
				height: 301px;
			}
		}
	}
	.row-4 {
		width: 418.7px;
		margin-right: 20px;
	}
	.row-5 {
		width: 431.7px;
	}
	.row-4,
	.row-5 {
		flex-direction: column;
		> div {
			border: 1px solid red;
			&:nth-of-type(1) {
				height: 223.5px;
				margin-bottom: 24px;
			}
			&:nth-of-type(2) {
				height: 314px;
				margin-bottom: 40px;
			}
			&:nth-of-type(3) {
				height: 301px;
			}
		}
	}

	//EHR系统-资产概览
	.ehr-asset-overview,
	.face-recognition-system,
	.intrusion-defense,
	.capacity-analysis {
		.chart-wrapper {
			margin-top: 10px;
			height: calc(100% - 49px);
		}
	}

	//防火墙防护事件
	.firewall-event {
		.chart-wrapper {
			height: calc(100% - 39px);
			transform: translateX(-40px);
		}
	}

	//能效分析
	.energy-efficiency-analysis {
		.block-content {
			margin-top: 10px;
			height: calc(100% - 49px);
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 17px 9px;
			.item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background: linear-gradient(
					to right,
					rgba(49, 82, 172, 0.2) 0%,
					rgba(49, 82, 172, 0.15) 50%,
					transparent 100%
				);
				.value-box {
					width: 89.7px;
					height: 88.7px;
					background: url('@/assets/img/overallOverview/bg-radius.png')
						no-repeat;
					background-size: 100% 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.value {
						font-size: 20px;
						line-height: 34px;
						font-family: DINPro-Bold;
						font-weight: bold;
					}
					.unit {
						font-size: 14px;
						line-height: 34px;
					}
					.label {
						font-family: Microsoft YaHei;
						font-weight: 400;
						font-size: 14px;
						color: #1ff1fc;
					}
				}
				.name-box {
					display: flex;
					align-items: center;
					margin-top: 2px;
					img {
						margin-right: 1px;
						height: 23.3px;
					}
					.name {
						font-family: Microsoft YaHei;
						font-weight: 400;
						font-size: 14px;
						color: #ffffff;
					}
				}
			}
		}
	}
}
