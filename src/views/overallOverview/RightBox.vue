<template>
	<div class="right-wrapper">
		<!--智慧能碳-价值观展现/智慧能源-控碳路径/AI-Agent-智能体价值贡献-->
		<!--<div class="row-1 row">-->
		<!--	&lt;!&ndash;智慧能碳-价值观展现&ndash;&gt;-->
		<!--	<SECValues :data="valuesData" />-->
		<!--	&lt;!&ndash;智慧能碳-控碳路径&ndash;&gt;-->
		<!--	<SECCarbonPath :data="carbonPathData" />-->
		<!--	&lt;!&ndash;AI-Agent-智能体价值贡献&ndash;&gt;-->
		<!--	<AIAgentValues :data="agentValuesData" />-->
		<!--</div>-->
		<!--用能统计/重点能耗设备/能效分析/产线能耗分析/阿米巴经营-->
		<!--<div class="row-2 row">-->
		<!--	&lt;!&ndash;用能统计&ndash;&gt;-->
		<!--	<EnergyStatistics :list="energyStatisticsData" />-->
		<!--	&lt;!&ndash;重点能耗设备&ndash;&gt;-->
		<!--	<KHEEquipment v-model="kheData" />-->
		<!--	&lt;!&ndash;能效分析&ndash;&gt;-->
		<!--	<div class="energy-efficiency-analysis">-->
		<!--		<BlockTitle title="能效分析" :background="imgMap[339]" />-->
		<!--		<div class="block-content">-->
		<!--			<div-->
		<!--				v-for="(item, index) in energyEfficiencyData"-->
		<!--				:key="index"-->
		<!--				class="item"-->
		<!--			>-->
		<!--				<div class="value-box">-->
		<!--					<div class="value value-text-gradient-1">-->
		<!--						{{ item.value }}<span class="unit">{{ item.unit }}</span>-->
		<!--					</div>-->
		<!--					<div class="label">{{ item.label }}</div>-->
		<!--				</div>-->
		<!--				<div class="name-box">-->
		<!--					<img :src="item.icon" alt="" />-->
		<!--					<span class="name">{{ item.name }}</span>-->
		<!--				</div>-->
		<!--			</div>-->
		<!--		</div>-->
		<!--	</div>-->
		<!--	&lt;!&ndash;产线能耗分析&ndash;&gt;-->
		<!--	<div class="capacity-analysis">-->
		<!--		<BlockTitle title="产线能耗分析" :background="imgMap[384]" />-->
		<!--		<div class="chart-wrapper"></div>-->
		<!--	</div>-->
		<!--	&lt;!&ndash;阿米巴经营&ndash;&gt;-->
		<!--	<AMiBaManage :data="amibaData" />-->
		<!--</div>-->
		<!--智联协同价值/DA系统概况/EHR系统-资产概览/邮件系统概览/EHR系统概况-->
		<div class="row-3 row">
			<!--智联协同价值-->
			<IICValues :list="iicvData" />
			<!--DA系统概况-->
			<div></div>
			<!--EHR系统-资产概览-->
			<div class="ehr-asset-overview">
				<BlockTitle title="EHR系统-资产概览" :background="imgMap[419]" />
				<div class="chart-wrapper">
					<LineChart
						class="chart"
						:chartData="barAndLineChart.chartData7.chartData"
						:yAxisName="barAndLineChart.chartData7.yAxisName"
						:unit="barAndLineChart.chartData7.unit"
						:colorList="barAndLineChart.chartData7.colorList"
					/>
				</div>
			</div>
			<!--邮件系统概览-->
			<EmailSystem />
			<!--EHR系统概况-->
			<EHRSysOverview :data="ehrSystemOverviewData" />
		</div>
		<!--智盾安全价值/工业资产总览/人脸识别系统概况-->
		<div class="row-4 row">
			<!--智盾安全价值-->
			<SafeValues :list="secValuesData" />
			<!--工业资产总览-->
			<div></div>
			<!--人脸识别系统概况-->
			<div class="face-recognition-system">
				<BlockTitle title="人脸识别系统概况" :background="imgMap[419]" />
				<div class="chart-wrapper">
					<LineChart
						class="chart"
						:chartData="barAndLineChart.chartData9.chartData"
						:yAxisName="barAndLineChart.chartData9.yAxisName"
						:unit="barAndLineChart.chartData9.unit"
						:colorList="barAndLineChart.chartData9.colorList"
					/>
				</div>
			</div>
		</div>
		<!--防火墙防护事件/入侵防御防护状态/安全态势概况-->
		<div class="row-5 row">
			<!--防火墙防护事件-->
			<div class="firewall-event">
				<BlockTitle title="防火墙防护事件" :background="imgMap[432]" />
				<div class="chart-wrapper">
					<RingChart
						:chart-data="firewallData"
						title="总件数"
						unit="件"
						:color-list="['#FFDB15', '#2447F7', '#10E8AA', '#24DCF7']"
					/>
				</div>
			</div>
			<!--入侵防御防护状态-->
			<div class="intrusion-defense">
				<BlockTitle title="入侵防御防护状态" :background="imgMap[432]" />
				<div class="chart-wrapper">
					<LineChart
						class="chart"
						:chartData="barAndLineChart.chartData10.chartData"
						:yAxisName="barAndLineChart.chartData10.yAxisName"
						:unit="barAndLineChart.chartData10.unit"
						:colorList="barAndLineChart.chartData10.colorList"
						:showSplitArea="barAndLineChart.chartData10.showSplitArea"
						:showYAxisLine="barAndLineChart.chartData10.showYAxisLine"
					/>
				</div>
			</div>
			<!--安全态势概况-->
			<SafeTrendOverview :data="safeOverviewData" />
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import LineChart from '@c/charts/LineChart.vue'
import RingChart from '@c/charts/RingChart.vue'
import HotWaterBoiler from '@img/overallOverview/icon-hw-boiler.png'
import SteamBoiler from '@img/overallOverview/icon-steam-boiler.png'

import AIAgentValues from './components/AgentValues.vue'
import AMiBaManage from './components/AMiBaManage.vue'
import EHRSysOverview from './components/EhrSysOverview.vue'
import EmailSystem from './components/EmailSystem.vue'
import EnergyStatistics from './components/EnergyStatistics.vue'
import IICValues from './components/IICValues.vue'
import KHEEquipment from './components/KHEEquipment.vue'
import SafeTrendOverview from './components/SafeTrendOverview.vue'
import SafeValues from './components/SafeValues.vue'
import SECCarbonPath from './components/SecCarbonPath.vue'
import SECValues from './components/SecValues.vue'
import { barAndLineChart } from './data'

const imgMap = inject('imgMap')

const updateOp = op => {
	console.log(op)
}

// 智慧能碳-价值观展现
const valuesData = reactive({
	list: [
		{
			label: '现年节省成本',
			value: 1150,
			unit: '万元'
		},
		{
			label: '建成后年节省成本',
			value: 2450,
			unit: '万元'
		},
		{
			label: '能源智慧化、信息化水平',
			value: '100%'
		},
		{
			label: '保障供用能安全可靠性',
			value: '100%'
		}
	],
	before: 'EER4.5', // 数字化建设前
	after: 'EER6.1', // 数字化建设后
	energyList: [
		{
			label: '能耗有效降低',
			value: 12,
			unit: '%'
		},
		{
			label: '能源费用减少',
			value: 20,
			unit: '%'
		},
		{
			label: '人员成本下降',
			value: 40,
			unit: '%'
		},
		{
			label: '碳排放量减少',
			value: 27,
			unit: '%'
		}
	]
})

// 智慧能碳-控碳路径
const carbonPathData = reactive({
	carbon: 2324.2, // 碳减排
	annual: 4324.2, // 年节煤
	chartData: {
		pv: {}, // 光伏发电碳减排
		server: {}, // 高效机房碳减排
		ap: {}, // 一级空压站房碳减排
		heat: {} // 余热回收供应热水碳减排
	}
})

// AI-Agent-智能体价值贡献
const agentValuesData = reactive({
	report: 156, // 生成报告数
	trend: 23,
	plan: 1247, // 规划巡检计划
	advance: 89, // 优化建议数
	ratio: 73.5, // 优化建议采纳率
	laborSaving: 2340, // 节省人工
	equivalent: 1.4, // 等效
	maintain: 85, // 维护节约成本
	speed: 2.5 // 响应速度
})

// 用能统计
const energyStatisticsData = ref([
	{
		label: '电',
		value: 5693,
		icon: 'IconElectricity',
		unit: 'MWh'
	},
	{
		label: '水',
		value: 5693,
		icon: 'IconWater',
		unit: 'm³'
	},
	{
		label: '天然气',
		value: 5693,
		icon: 'IconGas',
		unit: 'Nm³'
	},
	{
		label: '冷量',
		value: 5693,
		icon: 'IconSnow',
		unit: 'MWh'
	},
	{
		label: '热量',
		value: 5693,
		icon: 'IconHeat',
		unit: 'MWh'
	},
	{
		label: '氮气',
		value: 5693,
		icon: 'IconN2',
		unit: 'Nm³'
	},
	{
		label: '氧气',
		value: 5693,
		icon: 'IconO2',
		unit: '万Nm³'
	},
	{
		label: '10M纯水',
		value: 5693,
		icon: 'IconROWater',
		unit: 'm³'
	},
	{
		label: 'RO纯水',
		value: 5693,
		icon: 'IconROWater',
		unit: 'm³'
	},
	{
		label: '蒸汽',
		value: 5693,
		icon: 'IconSteam',
		unit: 't'
	}
])

// 重点能耗设备
const kheData = reactive({
	system: 'coolSys',
	systemOptions: [
		{
			label: '冷冻系统',
			value: 'coolSys'
		},
		{
			label: '空压系统',
			value: 'airPush'
		}
	],
	device: 'cool',
	deviceOptions: {
		coolSys: [
			{
				label: '冷冻机',
				value: 'cool',
				unit: 'COP'
			}
		],
		airPush: [
			{
				label: '干燥机',
				value: 'desiccant',
				unit: 'kWh'
			},
			{
				label: '空压机',
				value: 'airCompressor',
				unit: '单耗'
			}
		]
	},
	rankList: [
		{
			name: '7#冷冻机',
			value: 56.07
		},
		{
			name: '6#冷冻机',
			value: 56.06
		},
		{
			name: '5#冷冻机',
			value: 56.05
		}
	],
	chartData: []
})

// 能效分析
const energyEfficiencyData = ref([
	{
		name: '空压站',
		value: 0.107,
		label: '单耗',
		icon: HotWaterBoiler
	},
	{
		name: '冷冻站',
		value: 6.29,
		label: 'EER',
		icon: HotWaterBoiler
	},
	{
		name: '蒸汽锅炉',
		value: 111.16,
		label: '热效率',
		unit: '%',
		icon: SteamBoiler
	},
	{
		name: '热水锅炉',
		value: 104.92,
		label: '热效率',
		unit: '%',
		icon: HotWaterBoiler
	}
])

// 阿米巴经营
const amibaData = ref([
	{
		name: '103冷冻站',
		actual: 95,
		target: 85,
		onYear: 0.51,
		onMonth: 0.51
	},
	{
		name: '103公用',
		actual: 70,
		target: 60,
		onYear: 0.51,
		onMonth: 0.51
	},
	{
		name: '103空压站',
		actual: 98,
		target: 88,
		onYear: -0.51,
		onMonth: -0.51
	},
	{
		name: '106综合水站房',
		actual: 68,
		target: 60,
		onYear: -0.51,
		onMonth: -0.51
	},
	{
		name: '110危化库',
		actual: 85,
		target: 75,
		onYear: 0.51,
		onMonth: 0.51
	},
	{
		name: '104B配料站房',
		actual: 90,
		target: 80,
		onYear: -0.51,
		onMonth: -0.51
	}
])

// 智联协同价值
const iicvData = ref([
	{
		label: '审批效率提升',
		value: 90,
		icon: 'IconBar',
		unit: '%'
	},
	{
		label: '数字化覆盖',
		value: 100,
		icon: 'IconTrend',
		unit: '%'
	},
	{
		label: '库存资金占用',
		value: -15,
		icon: 'IconMoney',
		unit: '%'
	},
	{
		label: '考勤精准度',
		value: 100,
		icon: 'IconAim',
		unit: '%'
	}
])

// DA系统概况
const daSystemOverviewData = ref([
	{
		label: '所有流程',
		value: 119346
	},
	{
		label: '待审核超时流程',
		value: 10
	},
	{
		label: '重启过的流程',
		value: 126
	},
	{
		label: '处理人无效流程',
		value: 1959
	},
	{
		label: '异常的流程',
		value: 37
	},
	{
		label: '运行的流程',
		value: 3317
	}
])

// EHR系统概况
const ehrSystemOverviewData = ref({
	staff: [
		{
			name: '正高级',
			value: 61
		},
		{
			name: '副高级',
			value: 183
		},
		{
			name: '中级',
			value: 165
		},
		{
			name: '初级',
			value: 314
		},
		{
			name: '工人',
			value: 423
		}
	],
	personnel: []
})

// 智盾安全价值
const secValuesData = ref([
	{
		label: '物理、逻辑隔离',
		value: 100,
		icon: 'IconLock',
		unit: '%'
	},
	{
		label: '人脸识别率',
		value: 100,
		icon: 'IconFace',
		unit: '%'
	},
	{
		label: '入网终端',
		value: 100,
		icon: 'IconServer',
		unit: '%',
		append: '受控'
	},
	{
		label: '核心涉密文件',
		value: 100,
		icon: 'IconSafe',
		unit: '%',
		append: '加密'
	}
])

// 工业资产总览
const industryAssetsData = ref([
	{
		label: '现场控制设备',
		value: 1099
	},
	{
		label: '其他',
		value: 14663
	},
	{
		label: '测试类型',
		value: 0
	},
	{
		label: '网络安全设备',
		value: 17
	},
	{
		label: '生产信息系统',
		value: 1242
	},
	{
		label: '工业主机设备',
		value: 574
	}
])

// 防火墙防护事件
const firewallData = ref([
	{
		name: '处理失败',
		value: 75
	},
	{
		name: '已信任',
		value: 15
	},
	{
		name: '处理成功',
		value: 180
	},
	{
		name: '暂不处理',
		value: 30
	}
])

// 安全态势概况
const safeOverviewData = reactive({
	weakPassword: 280, // 弱口令资产
	vuln: 1488, // 资产漏洞
	riskIndex: 67, // 风险指数
	abnormal: 715520, // 异常行为告警
	threat: 3220 // 威胁告警
})
</script>

<style lang="scss" scoped>
@import './right.scss';
</style>
