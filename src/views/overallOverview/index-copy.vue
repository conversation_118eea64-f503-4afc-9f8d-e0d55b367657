<template>
	<div class="page-container">
		<!--<div class="chart-wrapper">-->
		<!--	<LineChart-->
		<!--		:chart-data="chartData1"-->
		<!--		:color-list="colors"-->
		<!--		yAxisName="次数"-->
		<!--		unit="次"-->
		<!--	/>-->
		<!--</div>-->
		<!--<div class="chart-wrapper">-->
		<!--	<LineChart-->
		<!--		:chart-data="chartData"-->
		<!--		:color-list="colorList1"-->
		<!--		:show-split-area="false"-->
		<!--		:show-y-axis-line="false"-->
		<!--		:show-y-axis-split-line="true"-->
		<!--		yAxisName="个/片"-->
		<!--		unit="个/片"-->
		<!--	/>-->
		<!--</div>-->
		<!--<div class="ring-chart-wrapper">-->
		<!--	<RingChart3d-->
		<!--		:chart-data="ringChartData"-->
		<!--		:color-list="colors"-->
		<!--		:opacity="0.7"-->
		<!--	/>-->
		<!--</div>-->
		<!--<div class="ring-chart-wrapper">-->
		<!--	<RingChart :chart-data="ringChartData1" title="总件数" />-->
		<!--</div>-->
	</div>
</template>

<script setup>
import LineChart from '@c/charts/LineChart.vue'
import RingChart from '@c/charts/RingChart.vue'
import RingChart3d from '@c/charts/RingChart3d.vue'

import { genChartMockData, set16ToRgb } from '@/utils/public.js'

const colors = ['#1460c1', '#a0deff', '#ffa82c', '#ff8430', '#2fc5e9']

const xData = ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
const chartData1 = {
	xData,
	seriesData: [
		{
			name: 'Pop',
			data: genChartMockData(xData)
		},
		{
			name: 'Imap',
			data: genChartMockData(xData)
		},
		{
			name: 'Web',
			data: genChartMockData(xData)
		},
		{
			name: 'Smtp',
			data: genChartMockData(xData)
		},
		{
			name: 'Lunkr',
			data: genChartMockData(xData)
		}
	]
}

const xMonthData = [
	'1月',
	'2月',
	'3月',
	'4月',
	'5月',
	'6月',
	'7月',
	'8月',
	'9月',
	'10月',
	'11月',
	'12月'
]

const colorList1 = ['#4ce0fb', '#3a8bf0', '#6f50f3', '#60f478']
const chartData = {
	xData: xMonthData,
	seriesData: [
		{
			name: '结石',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			itemStyle: {
				barBorderRadius: [2, 2, 0, 0],
				borderWidth: 2,
				borderColor: 'transparent',
				color: colorList1[0]
			},
			data: genChartMockData(xMonthData)
		},
		{
			name: '铂金',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			itemStyle: {
				borderWidth: 2,
				borderColor: 'transparent',
				color: colorList1[1]
			},
			data: genChartMockData(xMonthData)
		},
		{
			name: '气泡',
			type: 'bar',
			barWidth: 10,
			stack: '结石',
			itemStyle: {
				borderWidth: 2,
				borderColor: 'transparent',
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [
						{
							offset: 0,
							color: set16ToRgb(colorList1[2], 1)
						},
						{
							offset: 0.6,
							color: set16ToRgb(colorList1[2], 0.6)
						},
						{
							offset: 1,
							color: set16ToRgb(colorList1[2], 0.1)
						}
					]
				}
			},
			data: genChartMockData(xMonthData)
		},
		{
			name: '修复数',
			smooth: false,
			data: genChartMockData(xMonthData)
		}
	]
}

const ringChartData = [
	{
		name: '正高级',
		value: 61
	},
	{
		name: '副高级',
		value: 183
	},
	{
		name: '中级',
		value: 165
	},
	{
		name: '初级',
		value: 314
	},
	{
		name: '工人',
		value: 423
	}
]

const ringChartData1 = [
	{
		name: '处理成功',
		value: 180
	},
	{
		name: '处理失败',
		value: 75
	},
	{
		name: '暂不处理',
		value: 30
	},
	{
		name: '已信任',
		value: 15
	}
]
</script>

<style scoped lang="scss">
.page-container {
	display: flex;
	flex-wrap: wrap;
}
.chart-wrapper {
	width: 3000px;
	height: 1200px;
}
.ring-chart-wrapper {
	width: 3000px;
	height: 1500px;
}
</style>
