<!--智慧能碳-控碳路径-->
<template>
	<div class="sec-carbon-path">
		<BlockTitle title="智慧能源-控碳路径" :background="imgMap[759]" />
		<div class="flex">
			<div class="left">
				<div>
					<img src="@/assets/img/overallOverview/icon-bar.png" alt="" />
					<div>
						<div class="label">年节煤</div>
						<div class="value-box">
							<span class="value">{{ data.annual }}</span>
							<span class="unit">T</span>
						</div>
					</div>
				</div>
				<div>
					<img src="@/assets/img/overallOverview/icon-bar.png" alt="" />
					<div>
						<div class="label">碳减排</div>
						<div class="value-box">
							<span class="value">{{ data.carbon }}</span>
							<span class="unit">T</span>
						</div>
					</div>
				</div>
			</div>
			<div class="right">
				<TabList
					v-model="currentTab"
					:list="tabList"
					@change="handleChangeTab"
				/>
				<div class="chart-wrapper">
					<LineChart
						class="chart"
						:chartData="barAndLineChart.chartData8.chartData"
						:yAxisName="barAndLineChart.chartData8.yAxisName"
						:unit="barAndLineChart.chartData8.unit"
						:colorList="barAndLineChart.chartData8.colorList"
						:areaStyle="barAndLineChart.chartData8.areaStyle"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import LineChart from '@c/charts/LineChart.vue'
import TabList from '@c/TabList.vue'

import { barAndLineChart } from '../data'

defineProps({
	data: {
		type: Object,
		default: () => {}
	}
})

const imgMap = inject('imgMap')

const tabList = [
	{
		label: '光伏发电碳减排',
		code: 'pv'
	},
	{
		label: '高效机房碳减排',
		code: 'server'
	},
	{
		label: '一级空压站房碳减排',
		code: 'ap'
	},
	{
		label: '余热回收供应热水碳减排',
		code: 'heat'
	}
]
const currentTab = ref('pv')

// tab自动轮播
const timer = shallowRef(null)
const playDuration = 5000

const handleChangeTab = () => {
	clearAutoPlay()
	initAutoPlay()
}

const initAutoPlay = () => {
	timer.value = setInterval(() => {
		const index = tabList.findIndex(item => item.code === currentTab.value)
		if (index < tabList.length - 1) {
			currentTab.value = tabList[index + 1].code
		} else {
			currentTab.value = tabList[0].code
		}
	}, playDuration)
}
const clearAutoPlay = () => {
	if (timer.value) {
		clearInterval(timer.value)
		timer.value = null
	}
}

onMounted(() => {
	initAutoPlay()
})

onBeforeUnmount(() => {
	clearAutoPlay()
})
</script>

<style lang="scss" scoped>
.sec-carbon-path {
	.flex {
		display: flex;
		align-items: center;
		gap: 12px;
		margin-top: 10px;
		.left {
			width: 193.3px;
			height: 225px;
			background: url('@/assets/img/overallOverview/bg-block-divider.png')
				no-repeat;
			background-size: 100% 100%;
			> div {
				width: 100%;
				height: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				img {
					width: 45.3px;
					height: 51.3px;
					margin-right: 13px;
				}
				> div {
					.label {
						font-family: Microsoft YaHei;
						font-weight: 400;
						font-size: 18px;
						color: #ffffff;
					}
					.value {
						font-family: DINPro-Bold;
						font-weight: 900;
						font-size: 26px;
						color: #ffffff;
						background: linear-gradient(
							180deg,
							#ffffff 0%,
							#c3deff 50%,
							#5ea6ff 100%
						);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
					.unit {
						font-family: Microsoft YaHei;
						font-weight: 400;
						font-size: 14px;
						color: #97a7b2;
						margin-left: 10px;
					}
				}
			}
		}
		.right {
			width: 553px;
			height: 242px;
			.tab-list {
				justify-content: space-around;
			}
			.chart-wrapper {
				height: calc(100% - 26px);
			}
		}
	}
}
</style>
