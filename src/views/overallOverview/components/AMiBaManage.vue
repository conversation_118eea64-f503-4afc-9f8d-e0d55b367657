<!--阿米巴经营-->
<template>
	<div>
		<BlockTitle title="阿米巴经营" :background="imgMap[419]" />
		<div class="block-content">
			<div class="table-header row">
				<div class="value">
					<div>
						<span></span>
						实际值
					</div>
					<div>
						<span></span>
						计划值
					</div>
				</div>
				<div class="on-year">同比</div>
				<div class="on-month">环比</div>
			</div>
			<div class="table-body">
				<div v-for="(item, index) in data" :key="index" class="row">
					<div class="name text-overflow" :title="item.name">
						{{ item.name }}
					</div>
					<el-tooltip effect="dark">
						<template #content>
							<div>实际值: {{ item.actual }}</div>
							<div>计划值: {{ item.target }}</div>
						</template>
						<div class="value">
							<!--实际值-->
							<el-progress class="actual" :percentage="item.actual" />
							<!--计划值-->
							<el-progress class="target" :percentage="item.target" />
						</div>
					</el-tooltip>
					<div :class="['on-year', item.onYear > 0 ? 'up' : 'down']">
						<el-icon v-if="item.onYear > 0"><CaretTop /></el-icon>
						<el-icon v-else><CaretBottom /></el-icon>
						<span>{{ Math.abs(item.onYear) }}%</span>
					</div>
					<div :class="['on-month', item.onMonth > 0 ? 'up' : 'down']">
						<el-icon v-if="item.onMonth > 0"><CaretTop /></el-icon>
						<el-icon v-else><CaretBottom /></el-icon>
						<span>{{ Math.abs(item.onMonth) }}%</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'

import { CaretTop, CaretBottom } from '@element-plus/icons-vue'

defineProps({
	data: {
		type: Array,
		default: () => []
	}
})
const imgMap = inject('imgMap')
</script>

<style lang="scss" scoped>
.block-content {
	margin-top: 10px;
	height: calc(100% - 42px);
	.row {
		display: flex;
		align-items: center;
	}
	.on-year,
	.on-month {
		width: 70px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.table-header {
		width: 100%;
		justify-content: space-between;
		> div {
			font-family: AlibabaPuHuiTi_2_55_Regular;
			font-weight: normal;
			font-size: 14px;
			color: #a9c2c4;
		}
		.value {
			flex: 1;
			display: flex;
			align-items: center;
			> div {
				width: 71px;
				display: flex;
				align-items: center;
				justify-content: center;
				span {
					display: inline-block;
					width: 6px;
					height: 6px;
					border-radius: 50%;
					background: #0079e9;
					margin-right: 6px;
				}
				&:last-of-type {
					span {
						background: #00e8af;
					}
				}
			}
		}
	}
	.table-body {
		.row {
			height: 39px;
			.name {
				width: 70px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 14px;
				color: #a9c2c4;
			}
			.value {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 3px;
				:deep(.el-progress) {
					height: 6px;
					.el-progress-bar,
					.el-progress-bar__inner,
					.el-progress-bar__outer {
						height: 6px;
					}
					.el-progress__text {
						display: none;
					}
					.el-progress-bar__outer {
						background: transparent;
					}
					.el-progress-bar__inner {
						border-radius: 3px;
					}
					&.actual .el-progress-bar__inner {
						background: linear-gradient(
							to right,
							rgba(0, 80, 180, 0.05) 0%,
							#0079ea 100%
						);
					}
					&.target .el-progress-bar__inner {
						background: linear-gradient(
							to right,
							rgba(0, 234, 176, 0.05) 0%,
							rgba(0, 234, 176, 0.99) 100%
						);
					}
				}
			}
			.on-year,
			.on-month {
				font-family: AlibabaPuHuiTi_2_55_Regular;
				font-weight: normal;
				font-size: 14px;
				&.up {
					color: #ff5353;
				}
				&.down {
					color: #00eab0;
				}
			}
		}
	}
}
</style>
