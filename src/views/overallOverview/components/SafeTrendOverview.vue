<!--安全态势概况-->
<template>
	<div>
		<BlockTitle title="安全态势概况" :background="imgMap[432]" />
		<div class="block-content">
			<div v-for="(item, index) in list" :key="index" class="item">
				<div class="value">{{ formatterValue(data[item.valKey]) }}</div>
				<div class="label">{{ item.label }}</div>
				<div class="line"></div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'

import { thousandBitSeparator } from '@/utils/filters.js'

defineProps({
	data: {
		type: Object,
		default: () => {}
	}
})
const imgMap = inject('imgMap')

const formatterValue = val => {
	if (val === '' || val === null || val === undefined) {
		return ''
	}
	if (val > 10000) {
		return thousandBitSeparator(val)
	}
	return val
}

const list = [
	{
		label: '弱资产口令',
		valKey: 'weakPassword'
	},
	{
		label: '资产漏洞',
		valKey: 'vuln'
	},
	{
		label: '风险指数',
		valKey: 'riskIndex'
	},
	{
		label: '异常行为告警',
		valKey: 'abnormal'
	},
	{
		label: '威胁告警',
		valKey: 'threat'
	}
]
</script>

<style lang="scss" scoped>
.block-content {
	width: 423.7px;
	height: calc(100% - 58px);
	margin: 19px auto 0;
	background: url('@/assets/img/overallOverview/bg-safe-trend.png') no-repeat;

	position: relative;

	.item {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2px;
		.value {
			font-family: DINPro-Bold;
			font-weight: bold;
			font-size: 19px;
			color: #fefeff;
		}
		.label {
			font-family: Microsoft YaHei;
			font-weight: 400;
			font-size: 15px;
			color: #41cbc6;
		}
		.line {
			width: 2px;
			border: 1px dashed;
			border-image: linear-gradient(
					to bottom,
					#41cbc6 0%,
					#2c9196 50%,
					#04203a 100%
				)
				1;
		}

		&:nth-of-type(1) {
			top: 60px;
			left: 7px;
			.line {
				height: 66px;
			}
		}
		&:nth-of-type(2) {
			top: 29px;
			left: 91px;
			.line {
				height: 76px;
			}
		}
		&:nth-of-type(3) {
			top: 13px;
			left: 186px;
			.line {
				height: 76px;
			}
			.label {
				color: #ffac28;
			}
		}
		&:nth-of-type(4) {
			top: 30px;
			right: 71px;
			.line {
				height: 86px;
			}
		}
		&:nth-of-type(5) {
			top: 71px;
			right: 5px;
			.line {
				height: 60px;
			}
		}
	}
}
</style>
