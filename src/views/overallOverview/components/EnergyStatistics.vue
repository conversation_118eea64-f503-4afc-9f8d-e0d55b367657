<!--用能统计-->
<template>
	<div class="energy-statistics">
		<BlockTitle title="用能统计" :background="imgMap[809]" />
		<div class="content">
			<div v-for="(item, index) in list" :key="index" class="item">
				<img :src="iconMap[item.icon]" alt="" />
				<div class="right">
					<div>
						<span class="label">{{ item.label }}</span>
						<span class="unit">{{ item.unit }}</span>
					</div>
					<div class="value value-text-gradient-1">{{ item.value }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'

import IconElectricity from '@/assets/img/overallOverview/icon-electricity.png'
import IconGas from '@/assets/img/overallOverview/icon-gas.png'
import IconHeat from '@/assets/img/overallOverview/icon-heat.png'
import IconN2 from '@/assets/img/overallOverview/icon-n2.png'
import IconO2 from '@/assets/img/overallOverview/icon-o2.png'
import IconSnow from '@/assets/img/overallOverview/icon-snow.png'
import IconSteam from '@/assets/img/overallOverview/icon-steam.png'
import IconROWater from '@/assets/img/overallOverview/icon-water-gas.png'
import IconWater from '@/assets/img/overallOverview/icon-water.png'

defineProps({
	list: {
		type: Array,
		default: () => []
	}
})

const imgMap = inject('imgMap')

const iconMap = {
	IconElectricity,
	IconGas,
	IconHeat,
	IconN2,
	IconO2,
	IconSnow,
	IconSteam,
	IconROWater,
	IconWater
}
</script>

<style lang="scss" scoped>
.content {
	margin-top: 16px;
	width: 803px;
	height: 162px;
	border-radius: 8px;
	border: 2px solid #104374;
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	.item {
		display: flex;
		align-items: center;
		padding-left: 30px;

		img {
			margin-right: 6px;
		}

		.label {
			font-family: Microsoft YaHei;
			font-weight: 400;
			font-size: 16px;
			color: #ffffff;
		}
		.unit {
			font-family: Microsoft YaHei;
			font-weight: 400;
			font-size: 16px;
			color: #999999;
			margin-left: 4px;
		}
		.value {
			font-family: DINPro-Bold;
			font-weight: bold;
			font-size: 24px;
			line-height: 35px;
		}
	}
}
</style>
