<!--AI-Agent-智能体价值贡献-->
<template>
	<div>
		<BlockTitle title="AI-Agent-智能体价值贡献" :background="imgMap[759]" />
		<div class="flex">
			<div class="left">
				<img src="@/assets/img/overallOverview/ai-agent.png" alt="" />
				<div class="status">运行中</div>
			</div>
			<div class="right">
				<div class="info">
					<div>
						<span class="label">生成报告数</span>
						<span class="value">{{ data.report }}</span>
					</div>
					<div class="desc">
						本月{{ data.trend ? '+' : '-' }}{{ Math.abs(data.trend) }}
					</div>
				</div>
				<div class="info">
					<div>
						<span class="label">规划巡检计划</span>
						<span class="value">{{ data.plan }}</span>
					</div>
					<div class="desc">自动生成巡检计划</div>
				</div>
				<div class="info">
					<div>
						<span class="label">优化建议数</span>
						<span class="value">{{ data.advance }}</span>
					</div>
					<div class="desc">采纳率{{ data.ratio }}%</div>
				</div>
				<div class="info">
					<div>
						<span class="label">节省人工(h)</span>
						<span class="value">{{ data.laborSaving }}</span>
					</div>
					<div class="desc">等效{{ data.equivalent }}人年</div>
				</div>
				<div class="info">
					<div>
						<span class="label">维护节约成本(万)</span>
						<span class="value">{{ data.advance }}</span>
					</div>
					<div class="desc">预防性维护收益</div>
				</div>
				<div class="info">
					<div>
						<span class="label">响应速度(h)</span>
						<span class="value">{{ data.speed }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'

defineProps({
	data: {
		type: Object,
		default: () => {}
	}
})

const imgMap = inject('imgMap')
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
	align-items: center;
	gap: 24px;
	margin: 6px 5px 0 14px;
	.left {
		display: flex;
		flex-direction: column;
		align-items: center;
		img {
			width: 127.3px;
			height: 150px;
		}
		.status {
			font-family: Microsoft YaHei;
			font-weight: bold;
			font-size: 16px;
			color: #00ff72;
			margin-top: 13px;
			padding-left: 20px;
			position: relative;
			&::before {
				content: '';
				width: 9px;
				height: 9px;
				border-radius: 50%;
				background: #00ff72;
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
	.right {
		width: 589.7px;
		height: 186.4px;
		background: url('@/assets/img/overallOverview/bg-agent.png') no-repeat;
		background-size: 100% 100%;

		display: flex;
		flex-wrap: wrap;
		column-gap: 10px;
		padding: 0 20px 0 33px;
		.info {
			width: 170px;
			height: 93.2px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			.label {
				font-family: YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 16px;
				color: #ffffff;
				line-height: 19px;
			}
			.value {
				font-family: DINPro-Bold;
				font-weight: bold;
				font-size: 20px;
				color: #ffffff;
				background: linear-gradient(
					180deg,
					#ffffff 0%,
					#bcdaff 50%,
					#4a9bff 100%
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				margin-left: 10px;
			}
			.desc {
				margin-top: 12px;
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 14px;
			}

			&:nth-of-type(1) {
				.desc {
					color: #28e8ff;
				}
			}
			&:nth-of-type(2) {
				.desc {
					color: #88ff28;
				}
			}
			&:nth-of-type(3) {
				.desc {
					color: #c066ff;
				}
			}
			&:nth-of-type(4) {
				.desc {
					color: #3fea8c;
				}
			}
			&:nth-of-type(5) {
				.desc {
					color: #ffb628;
				}
			}
		}
	}
}
</style>
