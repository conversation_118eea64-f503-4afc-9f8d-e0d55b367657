<!--数智化效益体现-->
<template>
	<div>
		<BlockTitle title="数智化效益体现" :background="imgMap[586]" />
		<div class="block-content">
			<div v-for="(item, index) in list" :key="index" class="item">
				<img :src="iconMap[item.icon]" alt="" />
				<div class="label">{{ item.label }}</div>
				<div class="value-box value-text-gradient">
					<span class="before">{{ item.before }}</span>
					<span class="value"
						>{{ item.label === '产品追溯时间' ? '≤' : '' }}{{ item.value
						}}{{ item.unit }}</span
					>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import IconCost from '@img/overallOverview/icon-cost.png'
import IconDashboard from '@img/overallOverview/icon-dashboard.png'
import IconDeviceError from '@img/overallOverview/icon-device-error.png'
import IconRepair from '@img/overallOverview/icon-repair.png'
import IconTime from '@img/overallOverview/icon-time.png'
import IconArrowUp from '@img/overallOverview/icon-up-arrow.png'

defineProps({
	list: {
		type: Array,
		default: () => []
	}
})

const imgMap = inject('imgMap')

const iconMap = {
	IconCost,
	IconDashboard,
	IconDeviceError,
	IconRepair,
	IconTime,
	IconArrowUp
}
</script>

<style lang="scss" scoped>
.block-content {
	margin-top: 7px;
	height: calc(100% - 46px);
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 7px;
		img {
			height: 28.7px;
		}
		.label {
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 22px;
			color: #ffffff;
			padding-left: 18px;
			background: url('@/assets/img/bg-label-large.png') no-repeat;
			background-size: 100% 100%;
		}
		.value-box {
			.before {
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 22px;
				margin-right: 6px;
			}
			.value {
				font-family: DINPro-Bold;
				font-weight: bold;
				font-size: 22px;
			}
		}
	}
}
</style>
