<!--智盾安全价值-->
<template>
	<div>
		<BlockTitle title="智盾安全价值" :background="imgMap[419]" />
		<div class="block-content">
			<div v-for="(item, index) in list" :key="index" class="item">
				<img :src="iconMap[item.icon]" alt="" />
				<div>
					<div class="label">{{ item.label }}</div>
					<div class="value-box value-text-gradient">
						<span class="value">{{ item.value }}{{ item.unit }}</span>
						<span v-if="item.append" class="append">{{ item.append }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import IconFace from '@img/overallOverview/icon-face.png'
import IconLock from '@img/overallOverview/icon-lock.png'
import IconSafe from '@img/overallOverview/icon-safe.png'
import IconServer from '@img/overallOverview/icon-server.png'

defineProps({
	list: {
		type: Array,
		default: () => []
	}
})

const imgMap = inject('imgMap')

const iconMap = {
	IconFace,
	IconLock,
	IconSafe,
	IconServer
}
</script>

<style lang="scss" scoped>
.block-content {
	height: calc(100% - 39px);
	display: grid;
	grid-template-columns: repeat(2, 1fr);

	.item {
		display: flex;
		align-items: center;

		img {
			margin-right: 9px;
		}

		.label {
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 20px;
			color: #ffffff;
			padding-left: 12px;
			background: url('@/assets/img/bg-label.png') no-repeat;
			background-size: 100% 100%;
		}
		.value-box {
			padding-left: 12px;
		}
		.value {
			font-family: DINPro-Bold;
			font-weight: 900;
			font-size: 28px;
		}
		.append {
			font-family: Microsoft YaHei;
			font-weight: 400;
			font-size: 14px;
			margin-left: 8px;
		}
	}
}
</style>
