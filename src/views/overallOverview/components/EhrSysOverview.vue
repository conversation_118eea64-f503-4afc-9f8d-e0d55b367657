<!--EHR系统概况-->
<template>
	<div>
		<BlockTitle title="EHR系统概况" :background="imgMap[419]" />
		<TabList v-model="currentTab" :list="tabList" @change="changeTab" />
		<div class="chart-wrapper">
			<RingChart3d
				v-if="currentTab === 'staff'"
				:chart-data="data.staff"
				:color-list="['#1460C1', '#A0DEFF', '#FFA82C', '#FF8430', '#2FC5E9']"
				unit="人"
				:opacity="0.7"
			/>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import RingChart3d from '@c/charts/RingChart3d.vue'
import TabList from '@c/TabList.vue'

defineProps({
	data: {
		type: Object,
		default: () => {}
	}
})
const imgMap = inject('imgMap')

const tabList = [
	{
		label: '人员结构',
		code: 'staff'
	},
	{
		label: '到岗人数',
		code: 'personnel'
	}
]
const currentTab = ref('staff')

// tab自动轮播
const timer = shallowRef(null)
const playDuration = 5000

const changeTab = () => {
	clearAutoPlay()
	initAutoPlay()
}

const initAutoPlay = () => {
	timer.value = setInterval(() => {
		const index = tabList.findIndex(item => item.code === currentTab.value)
		if (index < tabList.length - 1) {
			currentTab.value = tabList[index + 1].code
		} else {
			currentTab.value = tabList[0].code
		}
	}, playDuration)
}
const clearAutoPlay = () => {
	if (timer.value) {
		clearInterval(timer.value)
		timer.value = null
	}
}

onMounted(() => {
	initAutoPlay()
})

onBeforeUnmount(() => {
	clearAutoPlay()
})
</script>

<style lang="scss" scoped>
:deep(.tab-list) {
	margin: 6px 0 10px 0;
	&::after {
		background: linear-gradient(to right, #004778 0%, transparent 100%);
	}
	.tab-item {
		width: 112px;
		text-align: center;
	}
}
.chart-wrapper {
	height: calc(100% - 79px);
}
.chart-container:has(.ring-chart) {
	&::after {
		content: '';
		width: 180px;
		height: 85px;
		background: linear-gradient(0deg, #28f0f7 0%, rgba(40, 240, 247, 0) 100%);
		border-radius: 50%;
		opacity: 0.15;
		position: absolute;
		bottom: 45px;
		left: 50%;
		transform: translateX(-50%);
		pointer-events: none;
	}
}
</style>
