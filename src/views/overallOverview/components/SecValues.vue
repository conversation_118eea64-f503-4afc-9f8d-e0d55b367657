智慧能碳-价值观展现
<template>
	<div class="sec-values">
		<BlockTitle title="智慧能碳-价值观展现" :background="imgMap[759]" />
		<div class="top-info">
			<div v-for="(item, index) in data.list" :key="index" class="item-info">
				<div class="label">{{ item.label }}</div>
				<div class="value-box">
					<span class="value value-text-gradient">{{ item.value }}</span>
					<span v-if="item.unit" class="unit">{{ item.unit }}</span>
				</div>
			</div>
		</div>
		<div class="flex">
			<div class="left">
				<div class="after">
					数字化建设后
					<span>{{ data.after }}</span>
				</div>
				<div class="arrow"></div>
				<div class="before">
					数字化建设前
					<span>{{ data.before }}</span>
				</div>
			</div>
			<div class="right">
				<div v-for="(item, index) in data.energyList" :key="index" class="item">
					<div class="label">{{ item.label }}</div>
					<div class="value">{{ item.value + item.unit }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'

defineProps({
	data: {
		type: Object,
		default: () => {}
	}
})

const imgMap = inject('imgMap')
</script>

<style lang="scss" scoped>
@mixin setBackground($url) {
	background: url($url) no-repeat;
	background-size: 100% 100%;
}

.sec-values {
	.top-info {
		width: 746px;
		height: 85px;
		margin-top: 25px;
		@include setBackground('@/assets/img/overallOverview/bg-values.png');

		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 85px;
		margin-left: 5px;

		.item-info {
			.label {
				width: fit-content;
				height: 24px;
				@include setBackground('@/assets/img/overallOverview/bg-label.png');
				font-family: Microsoft YaHei;
				font-weight: bold;
				font-size: 15px;
				line-height: 24px;
				color: #ffffff;
				padding-left: 18px;
			}
			.value-box {
				padding-left: 4px;
			}
			.value {
				font-family: DINPro-Bold;
				font-weight: bold;
				font-size: 26px;
			}
			.unit {
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 16px;
				color: #a5cbe3;
				margin-left: 4px;
			}
		}
	}
	.flex {
		display: flex;
		align-items: center;
		gap: 13px;
		margin: 9px 0 0 5px;
		.left {
			display: flex;
			flex-direction: column;
			align-items: center;
			.after,
			.before {
				width: 238px;
				height: 66.5px;
				display: flex;
				align-items: center;
				justify-content: center;
				@include setBackground('@/assets/img/overallOverview/bg-block.png');

				font-family: Microsoft YaHei;
				font-weight: bold;
				font-size: 20px;
				color: #ffffff;
				span {
					margin-left: 4px;
				}
			}
			.after span {
				color: #16e6ff;
			}
			.before span {
				color: #8dcbff;
			}
			.arrow {
				width: 43.7px;
				height: 29px;
				@include setBackground('@/assets/img/overallOverview/arrow-up.png');
			}
		}
		.right {
			width: 505px;
			height: 166.7px;
			@include setBackground('@/assets/img/overallOverview/bg-green.png');

			position: relative;

			.item {
				position: absolute;
				&:nth-of-type(1) {
					left: 53px;
					top: 26px;
				}
				&:nth-of-type(2) {
					top: 26px;
					right: 46px;
				}
				&:nth-of-type(3) {
					bottom: 20px;
					left: 53px;
				}
				&:nth-of-type(4) {
					bottom: 20px;
					right: 46px;
				}

				.label {
					font-family: Microsoft YaHei;
					font-weight: bold;
					font-size: 16px;
					color: #ffffff;
				}
				.value {
					font-family: DINPro-Bold;
					font-weight: bold;
					font-size: 26px;
					color: #b5caeb;
					background: linear-gradient(
						180deg,
						#c8d8f0 0%,
						#b3c8eb 50%,
						#a3bde6 100%
					);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}
}
</style>
