<!--重点能耗设备-->
<template>
	<div>
		<BlockTitle title="重点能耗设备" :background="imgMap[447]" />
		<div class="block-content">
			<div class="left">
				<div
					v-for="(item, index) in data.rankList"
					:key="index"
					class="rank-item left-light-block"
				>
					<img :src="rankImgMap[index + 1]" alt="" />
					<div>
						<div class="name">{{ item.name }}</div>
						<div class="value">
							{{ item.value }} <span class="unit">COP</span>
						</div>
					</div>
				</div>
			</div>
			<div class="chart-wrapper">
				<div class="select-wrapper">
					<BaseDropDown
						v-model="data.system"
						:options="data.systemOptions"
						@change="changeSystem"
					/>
					<BaseDropDown
						v-model="data.device"
						:options="deviceOptions"
						@change="changeDevice"
					/>
				</div>
				<!--<BarChart3d />-->
			</div>
		</div>
	</div>
</template>

<script setup>
import BaseDropDown from '@c/BaseDropDown.vue'
import BlockTitle from '@c/BlockTitle.vue'
import BarChart3d from '@c/charts/BarChart3d.vue'
import RankFirst from '@img/overallOverview/rank-first.png'
import RankSecond from '@img/overallOverview/rank-second.png'
import RankThird from '@img/overallOverview/rank-third.png'

const emit = defineEmits(['change'])

const imgMap = inject('imgMap')

const rankImgMap = {
	1: RankFirst,
	2: RankSecond,
	3: RankThird
}

const data = defineModel({
	type: Object,
	default: () => {}
})

const deviceOptions = computed(() => {
	return data.value.deviceOptions[data.value.system]
})

// 设备切换
const changeDevice = () => {
	emit('change')
}

// 系统切换
const changeSystem = () => {
	data.value.device = deviceOptions.value[0].value
	emit('change')
}
</script>

<style lang="scss" scoped>
.block-content {
	margin-top: 6px;
	height: calc(100% - 45px);
	display: flex;
	align-items: center;
	gap: 11px;
	.left {
		width: 128px;
		height: 100%;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.rank-item {
			flex: 1;
			padding-left: 11px;
			display: flex;
			align-items: center;
			gap: 9px;
			img {
				width: 23.7px;
				height: 29.7px;
			}
			.name {
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
			}
			.value {
				font-family: DINPro-Bold;
				font-weight: bold;
				font-size: 16px;
				color: #d9e3ec;
			}
			.unit {
				font-family: DINCond-Regular;
				font-weight: normal;
			}

			&:nth-of-type(1) {
				.value {
					color: #fff142;
				}
			}
			&:nth-of-type(3) {
				.value {
					color: #efddb6;
				}
			}
		}
	}
	.chart-wrapper {
		width: calc(100% - 139px);
		height: 100%;
		position: relative;
		.select-wrapper {
			display: flex;
			align-items: center;
			gap: 17px;
			position: absolute;
			right: 3px;
			top: 2px;
		}
	}
}
</style>
