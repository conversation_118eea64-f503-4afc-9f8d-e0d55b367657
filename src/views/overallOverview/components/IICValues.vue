<!--智联协同价值-->
<template>
	<div>
		<BlockTitle title="智联协同价值" :background="imgMap[879]" />
		<div class="block-content">
			<div v-for="(item, index) in list" :key="index" class="item">
				<img :src="iconMap[item.icon]" alt="" />
				<div class="label">{{ item.label }}</div>
				<div class="value-box value-text-gradient">
					<span v-if="item.label === '库存资金占用'" class="trend">{{
						item.value > 0 ? '增长' : '下降'
					}}</span>
					<span class="value">{{ Math.abs(item.value) }}{{ item.unit }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import IconAim from '@img/overallOverview/icon-aim.png'
import IconBar from '@img/overallOverview/icon-bar-large.png'
import IconMoney from '@img/overallOverview/icon-money.png'
import IconTrend from '@img/overallOverview/icon-trend.png'

defineProps({
	list: {
		type: Array,
		default: () => []
	}
})

const imgMap = inject('imgMap')

const iconMap = {
	IconAim,
	IconBar,
	IconMoney,
	IconTrend
}
</script>

<style lang="scss" scoped>
.block-content {
	width: 879.3px;
	height: 180.2px;
	background: url('@/assets/img/overallOverview/bg-iicv.png') no-repeat;
	background-size: 100% 100%;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		img {
			width: 61px;
			height: 67.7px;
		}
		.label {
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 24px;
			color: #ffffff;
			padding-left: 25px;
			background: url('@img/bg-label-large.png')
				no-repeat;
			background-size: 100% 100%;
			margin: 9px 0;
		}
		.value-box {
			.value {
				font-family: DINPro-Bold;
				font-weight: 900;
				font-size: 34px;
			}
			.trend {
				font-family: Microsoft YaHei;
				font-weight: 400;
				font-size: 20px;
				margin-right: 4px;
			}
		}
	}
}
</style>
