<!--邮件系统概览-->
<template>
	<div class="email-system">
		<BlockTitle title="邮件系统概览" :background="imgMap[447]" />
		<div class="tab-date">
			<span
				:class="['tab-list', curDateType === 'year' ? 'active' : '']"
				@click="curDateType = 'year'"
				>年度</span
			>
			<span
				:class="['tab-list', curDateType === 'month' ? 'active' : '']"
				@click="curDateType = 'month'"
				>月度</span
			>
		</div>
		<div class="bottom-wrapper">
			<div class="email-number">
				<p>
					<span>发送数</span>
					<span class="value-text-gradient">150231</span>
					<span>封</span>
				</p>
				<p>
					<span>接收数</span>
					<span class="value-text-gradient">150231</span>
					<span>封</span>
				</p>
				<p>
					<span>拦截数</span>
					<span class="value-text-gradient">150231</span>
					<span>封</span>
				</p>
			</div>

			<div class="chart-container">
				<el-select v-model="value">
					<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>

				<LineChart
					class="chart"
					:chartData="barAndLineChart.chartData11.chartData"
					:yAxisName="barAndLineChart.chartData11.yAxisName"
					:unit="barAndLineChart.chartData11.unit"
					:colorList="barAndLineChart.chartData11.colorList"
					:showSplitArea="barAndLineChart.chartData11.showSplitArea"
					:showYAxisLine="barAndLineChart.chartData11.showYAxisLine"
					@update-op="updateOp"
				/>
			</div>
		</div>
	</div>
</template>

<script setup>
import BlockTitle from '@c/BlockTitle.vue'
import LineChart from '@c/charts/LineChart.vue'

import { barAndLineChart } from '../data'

const imgMap = inject('imgMap')

const curDateType = ref('year')
const value = ref('用户统计')

const options = [
	{
		value: '用户统计',
		label: '用户统计'
	}
]

const handleChangeTab = code => {}

const updateOp = chartOption => {
	chartOption.legend.left = '15%'
}
</script>

<style lang="scss" scoped>
.email-system {
	width: 100%;
	height: 100%;
	position: relative;
}
.tab-date {
	position: absolute;
	top: 8px;
	right: 5px;
	width: 152px;
	height: 26px;
	background-color: rgba(75, 102, 156, 0);
	.tab-list {
		display: inline-block;
		width: 50%;
		height: 100%;
		text-align: center;
		line-height: 26px;
		color: #617294;
		font-size: 14px;
		&.active {
			background: linear-gradient(
				to top,
				rgba(44, 106, 233, 0.3),
				rgba(44, 106, 233, 0)
			);
			color: #c4d7ee;
		}
	}
}
.bottom-wrapper {
	width: 100%;
	height: calc(100% - 49px);
	margin-top: 10px;
	display: flex;
	flex-direction: column;

	.email-number {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 30px;
		margin: 0 10px;
		padding: 7px;
		background: linear-gradient(
			to top,
			rgba(0, 168, 255, 0.3) 0%,
			rgba(0, 168, 255, 0.2) 10%,
			rgba(0, 168, 255, 0.1) 30%,
			rgba(0, 168, 255, 0.05) 40%,
			rgba(0, 168, 255, 0) 50%
		);
		p {
			margin: 0;
			font-size: 12px;
			.value-text-gradient {
				margin: 0 5px;
				font-size: 18px;
				font-weight: 900;
			}
		}
	}

	.chart-container {
		position: relative;
		flex: 1;
		margin-top: 20px;
		.el-select {
			position: absolute;
			top: 0;
			right: 10px;
			width: 100px;
			border-radius: 4px;
			z-index: 1;
			:deep(.el-select__wrapper) {
				min-height: 18px;
				padding: 0 10px;
				box-shadow: 0 0 0 1px rgba(0, 191, 255, 0.5) inset;
				font-size: 13px;
				text-align: right;
				.el-select__selected-item {
					span {
						background: linear-gradient(
							180deg,
							rgba(255, 255, 255, 1) 0%,
							rgba(188, 218, 255, 1) 50%,
							rgba(90, 164, 255, 1) 100%
						);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
					}
				}
				.el-icon svg {
					color: #00bfff;
				}
			}
		}

		.chart {
			width: 100%;
			height: 100%;
		}
	}
}
</style>
