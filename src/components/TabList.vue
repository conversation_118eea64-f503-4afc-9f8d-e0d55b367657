<template>
	<div class="tab-list">
		<div
			v-for="(item, index) in list"
			:key="index"
			:class="['tab-item', { active: item.code === currentTab }]"
			@click="handleChangeTab(item.code)"
		>
			{{ item.label }}
		</div>
	</div>
</template>

<script setup>
defineProps({
	list: {
		type: Array,
		default: () => []
	}
})
const emit = defineEmits(['change'])

const currentTab = defineModel({
	type: [Number, String],
	default: ''
})

const handleChangeTab = code => {
	if (code !== currentTab.value) {
		currentTab.value = code
		emit('change')
	}
}
</script>

<style lang="scss" scoped>
.tab-list {
	width: 100%;
	display: flex;
	align-items: center;
	position: relative;
	&::after {
		content: '';
		width: 100%;
		height: 1px;
		background: rgba(143, 214, 255, 0.5);
		position: absolute;
		bottom: 0;
	}
	.tab-item {
		height: 24px;
		padding: 0 6px;
		cursor: pointer;

		font-family: Microsoft YaHei;
		font-weight: 400;
		font-size: 14px;
		color: #777777;
		line-height: 23px;

		&.active {
			color: #ffffff;
			background: linear-gradient(
				180deg,
				transparent 0%,
				rgba(1, 37, 68, 1) 30%,
				rgba(0, 52, 90, 1) 60%,
				rgba(0, 83, 137, 1) 100%
			);
		}
	}
}
</style>
