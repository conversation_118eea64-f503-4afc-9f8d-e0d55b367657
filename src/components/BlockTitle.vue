<template>
	<div
		class="block-title"
		:style="{
			'--block-title-bg': `url(${background}) no-repeat`
		}"
	>
		{{ title }}
		<slot></slot>
	</div>
</template>

<script setup>
defineProps({
	title: {
		type: String,
		default: ''
	},
	background: {
		type: [Object, String],
		default: () => {}
	}
})
</script>

<style lang="scss" scoped>
.block-title {
	width: 100%;
	height: 39px;
	font-family: YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 24px;
	color: #ffffff;
	line-height: 33px;
	padding-left: 32px;
	background: var(--block-title-bg) no-repeat;
	background-size: 100% 100%;
}
</style>
