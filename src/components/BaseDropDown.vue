<!--自定义下拉组件-->
<template>
	<el-dropdown
		trigger="click"
		popper-class="custom-dropdown-popper"
		@command="handleCommand"
	>
		<span class="el-dropdown-link">
			{{ currentText }}
			<el-icon>
				<CaretBottom />
			</el-icon>
		</span>
		<template #dropdown>
			<el-dropdown-menu>
				<el-dropdown-item
					v-for="(item, index) in options"
					:key="index"
					:class="{ active: item.value === current }"
					:command="item.value"
					>{{ item.label }}</el-dropdown-item
				>
			</el-dropdown-menu>
		</template>
	</el-dropdown>
</template>

<script setup>
import { CaretBottom } from '@element-plus/icons-vue'

const props = defineProps({
	options: {
		type: Array,
		default: () => []
	}
})
const emit = defineEmits(['change'])

const current = defineModel({
	type: [String, Number],
	default: () => ''
})

const currentText = computed(() => {
	return props.options.find(item => item.value === current.value)?.label || ''
})

const handleCommand = val => {
	current.value = val
	emit('change')
}
</script>

<style lang="scss" scoped>
.el-dropdown {
	min-width: 95px;
	height: 24px;
	border-radius: 6px;
	border: 1px solid #25bcc9;
	:deep(.el-dropdown-link) {
		width: 100% !important;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		font-size: 14px;
		color: #25f4fd;
	}
}
</style>

<style lang="scss">
.el-popper.el-dropdown__popper.custom-dropdown-popper {
	--el-border-color-light: #25bcc9;
	--el-bg-color-overlay: #041932;
	min-width: 95px;
	.active.el-dropdown-menu__item,
	.el-dropdown-menu__item:not(.is-disabled):focus,
	.el-dropdown-menu__item:not(.is-disabled):hover {
		color: #25bcc9;
		background: rgba(#25bcc9, 0.2);
	}
}
</style>
