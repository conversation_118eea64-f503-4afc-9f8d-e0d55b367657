<!-- 仪表盘图表 -->
<template>
	<div ref="chartRef" class="dashboard-chart"></div>
</template>

<script setup>
import * as echarts from 'echarts'

import { getResponsiveSize } from '@/utils/public.js'

const props = defineProps({
	value: {
		type: Number,
		default: 86
	}
})

const chartRef = ref(null)
let myChart = null

const option = {
	series: [
		{
			type: 'pie',
			animation: false,
			emphasis: {
				disabled: true
			},
			tooltip: {
				show: false
			},
			radius: ['0%', '10%'],
			center: ['65%', '80%'],
			label: {
				show: false
			},
			labelLine: {
				show: false
			},
			data: [
				{
					value: 100,
					itemStyle: {
						color: '#FFF',
						shadowColor: '#1E91F8',
						shadowBlur: getResponsiveSize(30)
					}
				}
			]
		},
		{
			type: 'pie',
			animation: false,
			emphasis: {
				disabled: true
			},
			tooltip: {
				show: false
			},
			radius: ['0%', '7%'],
			center: ['65%', '80%'],
			label: {
				show: false
			},
			labelLine: {
				show: false
			},
			data: [
				{
					value: 100,
					itemStyle: {
						color: '#1E91F8'
					}
				}
			]
		},
		{
			type: 'gauge',
			radius: '40%',
			center: ['65%', '80%'],
			min: 0,
			max: 100,
			startAngle: 180,
			endAngle: 0,
			axisLine: {
				show: true,
				lineStyle: {
					width: '100%',
					color: [[1, '#162643']]
				}
			},
			progress: {
				show: true,
				width: 3,
				itemStyle: {
					color: '#33425A'
				}
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: false
			},
			axisLabel: {
				show: false
			},
			pointer: {
				show: false
			},
			detail: {
				show: false
			},
			data: [{ value: 100 }]
		},
		{
			type: 'gauge',
			startAngle: 180,
			endAngle: 0,
			radius: '100%',
			center: ['65%', '80%'],
			min: 0,
			max: 100,
			splitNumber: 15,
			z: 10,
			axisLine: {
				show: true,
				lineStyle: {
					width: getResponsiveSize(15),
					color: [[1, '#202F49']] // 颜色
				}
			},
			progress: {
				show: true,
				width: getResponsiveSize(15),
				itemStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						1,
						0,
						[
							{
								offset: 0,
								color: '#24FFC9'
							},
							{
								offset: 1,
								color: '#187FFF'
							}
						],
						false
					)
				}
			},
			axisTick: {
				show: true,
				length: getResponsiveSize(10),
				distance: getResponsiveSize(14),
				lineStyle: {
					color: '#425067',
					width: 1
				}
			},
			splitLine: {
				show: true,
				length: getResponsiveSize(-16),
				distance: getResponsiveSize(32),
				lineStyle: {
					color: '#616E87',
					width: 1
				}
			},
			pointer: {
				length: '70%',
				width: getResponsiveSize(12),
				offsetCenter: [0, -6],
				itemStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{ offset: 0, color: '#1E91F8' },
							{ offset: 1, color: 'rgba(30,145,248, 0)' }
						]
					}
				}
			},
			axisLabel: {
				show: true,
				distance: getResponsiveSize(-45),
				color: '#BAD3E5',
				fontSize: getResponsiveSize(16),
				formatter(value) {
					return value % 20 === 0 ? value : ''
				}
			},
			detail: {
				formatter: value => `{val|${value}%}\n{text|年综合效率}`,
				rich: {
					val: {
						fontSize: getResponsiveSize(30),
						fontWeight: 'bold',
						lineHeight: getResponsiveSize(50),
						color: '#1E91F8'
					},
					text: {
						fontSize: getResponsiveSize(16),
						color: '#FFFFFF'
					}
				},
				offsetCenter: ['-190%', '-50%'], // 把数值文字下移一些
				valueAnimation: true
			},
			data: [{ value: props.value }]
		}
	]
}

const resizeChart = () => {
	if (myChart) {
		myChart.resize()
	}
}

const getHorizontalLineGraphic = () => {
	const chartDom = chartRef.value
	const width = chartDom?.offsetWidth || 400
	const height = chartDom?.offsetHeight || 300

	// 仪表盘中心和半径
	const centerX = width * 0.65
	const centerY = height * 0.8
	const radius = Math.min(width, height) * 0.52 // 超出gauge的宽度

	// 横线起止点
	const x1 = centerX - radius
	const x2 = centerX + radius
	const y = centerY

	return {
		type: 'line',
		shape: {
			x1,
			y1: y,
			x2,
			y2: y
		},
		style: {
			stroke: '#364349',
			lineWidth: 2,
			shadowBlur: 0
		},
		z: 1,
		silent: true
	}
}

const getPointerCircleGraphic = value => {
	const chartDom = chartRef.value
	const width = chartDom?.offsetWidth || 400
	const height = chartDom?.offsetHeight || 300

	// 仪表盘中心和半径
	const centerX = width * 0.65
	const centerY = height * 0.8
	const radius = Math.min(width, height) * 0.47
	const startAngle = 180
	const endAngle = 0
	const min = 0
	const max = 100

	const angle =
		((startAngle - ((value - min) / (max - min)) * (startAngle - endAngle)) *
			Math.PI) /
		180

	const cx = centerX + radius * Math.cos(angle)
	const cy = centerY - radius * Math.sin(angle)

	return {
		type: 'circle',
		shape: {
			cx,
			cy,
			r: getResponsiveSize(6)
		},
		style: {
			fill: '#1E91F8',
			stroke: '#fff',
			lineWidth: getResponsiveSize(4),
			shadowColor: '#1E91F8',
			shadowBlur: 10
		},
		z: 100
	}
}

const setChartOption = value => {
	const optionWithGraphic = {
		...option,
		graphic: {
			elements: [getHorizontalLineGraphic(), getPointerCircleGraphic(value)]
		}
	}
	myChart.setOption(optionWithGraphic, true)
}

watch(
	() => props.value,
	newVal => {
		setChartOption(newVal)
	}
)

onMounted(() => {
	myChart = echarts.init(chartRef.value)
	setChartOption(props.value)
	window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
	if (myChart) {
		myChart.dispose()
	}
	window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped lang="scss">
.dashboard-chart {
	width: 100%;
	height: 100%;
}
</style>
