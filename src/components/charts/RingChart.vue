<template>
	<div ref="chartRef" class="ring-chart"></div>
</template>

<script setup>
import * as echarts from 'echarts'

const props = defineProps({
	chartData: {
		required: true,
		type: Object,
		default: () => {
			return {
				xData: [],
				seriesData: []
			}
		}
	},
	unit: {
		type: String,
		default: () => ''
	},
	colorList: {
		type: Array,
		default: () => ['#10e8aa', '#ffdb15', '#24dcf7', '#2447f7']
	},
	title: {
		type: String,
		default: () => ''
	},
	showLegend: {
		type: Boolean,
		default: () => true
	}
})

const emit = defineEmits(['updateOp'])

const chartRef = ref(null)
let myChart = null

// 刻度相关配置
const scaleConfig = reactive({
	max: 100, // 最大刻度值
	splitNumber: 12, // 刻度分割数
	showLabel: true, // 是否显示刻度标签
	lineLength: 15 // 刻度线长度
})

const drawChart = () => {
	if (!chartRef.value) return
	if (myChart) {
		myChart.dispose()
	}
	myChart = echarts.init(chartRef.value)

	const { chartData, colorList, title } = props

	const total = chartData.reduce((acc, item) => acc + item.value, 0)
	const option = {
		title: {
			text: total,
			left: 'center',
			top: '34%',
			textStyle: {
				color: '#fff',
				fontSize: 20,
				fontWeight: 'normal'
			},
			subtext: title,
			subtextStyle: {
				color: '#fff',
				fontSize: 16,
				fontWeight: 'normal'
			}
		},
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b}: {c} ({d}%)'
		},
		legend: {
			show: props.showLegend,
			right: 10,
			top: 'center',
			orient: 'vertical',
			icon: 'rect',
			itemWidth: 12,
			itemHeight: 12,
			textStyle: {
				color: '#94aabc',
				fontWeight: 'normal',
				rich: {
					a: {
						verticalAlign: 'top'
					}
				},
				padding: [0, 0, -4, 0]
			},
			data: chartData.map(item => item.name)
		},
		series: [
			{
				type: 'gauge',
				startAngle: 90,
				endAngle: -269.9,
				pointer: {
					show: false
				},
				progress: {
					show: true
				},
				axisLine: {
					lineStyle: {
						width: 10,
						color: [[1, 'rgba(23, 108, 244, 0.2)']]
					}
				},
				splitLine: {
					distance: -10,
					show: true,
					length: scaleConfig.lineLength / 1.2,
					lineStyle: {
						width: 2,
						color: '#24A7FF'
					}
				},
				axisTick: {
					distance: -10,
					show: true,
					length: scaleConfig.lineLength / 2,
					lineStyle: {
						width: 1,
						color: '#24A7FF'
					}
				},
				axisLabel: {
					show: scaleConfig.showLabel,
					distance: scaleConfig.lineLength + 10,
					fontSize: 12,
					formatter(value) {
						// 只显示主要刻度的标签
						if (
							value % (scaleConfig.max / (scaleConfig.splitNumber / 5)) ===
							0
						) {
							return ''
						}
						return ''
					}
				},
				title: {
					show: false
				},
				detail: {
					show: false
				},
				min: 0,
				max: scaleConfig.max,
				splitNumber: scaleConfig.splitNumber
			},
			{
				name: '',
				type: 'pie',
				radius: ['40%', '60%'], // 控制环形的大小和厚度
				center: ['50%', '50%'],
				avoidLabelOverlap: false,
				itemStyle: {
					borderColor: '#011022',
					borderWidth: 3
				},
				label: {
					formatter: `{d}%`,
					color: 'inherit'
				},
				emphasis: {
					label: {
						show: true,
						fontSize: 16,
						fontWeight: 'bold'
					}
				},
				labelLine: {
					show: true,
					length: 20,
					length2: 15
				},
				tooltip: {
					backgroundColor: '#033b77',
					borderColor: '#21f2c4',
					textStyle: {
						color: '#fff',
						fontSize: 13
					},
					formatter: params => {
						return `${params.name}<br/>${params.value} ${props.unit}`
					}
				},
				data: chartData.map((item, index) => ({
					value: item.value,
					name: item.name,
					itemStyle: {
						color: colorList[index]
					}
				}))
			}
		]
	}

	emit('updateOp', option)
	myChart.clear()
	myChart.setOption(option)
}

watch(
	() => props.chartData,
	val => {
		nextTick(() => {
			drawChart()
		})
	},
	{
		immediate: true,
		deep: true
	}
)

const resizeChart = () => {
	if (myChart) {
		myChart.resize()
	}
}

onMounted(() => {
	window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
	if (myChart) myChart.dispose()
	window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.ring-chart {
	width: 100%;
	height: 100%;
}
</style>
