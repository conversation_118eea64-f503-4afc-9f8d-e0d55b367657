@use './reset.css';
@use './element-plus.scss';

#app {
	height: 100vh;
	background-color: #011022;
}

// 文字超出...
.text-overflow {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

// 数值文字渐变色
.value-text-gradient {
	background: linear-gradient(
		180deg,
		rgba(255, 255, 255, 1) 0%,
		rgba(188, 218, 255, 1) 50%,
		rgba(90, 164, 255, 1) 100%
	);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.value-text-gradient-1 {
	background: linear-gradient(180deg, #fafffe 0%, #acf7f6 50%, #30cbfd 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

::-webkit-scrollbar {
	width: 0;
	height: 10px;
}

::-webkit-scrollbar-track {
	box-shadow: 0px 1px 2px rgba($color: #0089f0, $alpha: 0.1) inset;
	border-radius: 5px;
	background-color: rgba($color: #0089f0, $alpha: 0.1);
}

::-webkit-scrollbar-thumb {
	box-shadow: 0px 1px 2px rgba(0, 137, 240, 0.6) inset;
	border-radius: 8px;
	background-color: rgba(0, 137, 240, 0.6);
}

// 左侧高亮边框，右侧渐变
.left-light-block {
	position: relative;
	background: linear-gradient(
		45deg,
		rgba(49, 82, 172, 0.2) 0%,
		rgba(49, 82, 172, 0.1) 50%,
		transparent 100%
	);
	&::before {
		content: '';
		position: absolute;
		width: 3px;
		height: calc(100% + 2px);
		background: #00bfff;
		left: 0;
		top: -1px;
	}
}
