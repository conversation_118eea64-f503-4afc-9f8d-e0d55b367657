:root {
	// 这里可以设置你自定义的颜色变量
	// 这个是element主要按钮:active的颜色，当主题更改后此变量的值也随之更改
	--el-color-primary-dark: #0d84ff;
	// element plus 2.1.0 禁用文本色值和正常文本色值无法区分问题
	--el-text-color-disabled: #ccc;
	--el-bg-color-overlay: rgba(#fff, 0);
}


.el-picker__popper {
	width: 972px;
	height: 1029px;
	background: #0c2245 !important;
	border-color: rgba(142, 199, 225, 0.8) !important;
	.el-picker-panel.el-date-picker,
	.el-picker-panel__body-wrapper,
	.el-picker-panel__body {
		width: 100%;
		height: 100%;
	}
	.el-date-picker__header {
		padding: 36px 36px 0;
		height: 126px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		span,
		.el-icon {
			font-size: 50px;
			color: #7988A1;
		}
	}
	.el-picker-panel__content {
		width: 100% !important;
		height: calc(100% - 126px);
		margin: 0 !important;
		table {
			width: 100%;
			height: 100%;
			tr {
				height: 126px;
			}
			th {
				font-size: 50px;
				color: #7988A1;
				border-color: rgba(142, 199, 225, 0.8) !important;
			}
			.el-date-table-cell__text {
				width: fit-content !important;
				font-size: 50px;
			}
		}
	}
}
