/**
 * 对数字进行四舍五入，保留两位小数的过滤器
 * @param {*} value 		目标数据
 * @param {*} fixedNum 	保留的小数位数
 * @returns
 */
export function keepNumBitFilter(value, fixedNum = 2) {
	if (Number.isNaN(value) || value === Infinity || value === -Infinity) {
		return '-'
	}
	let realVal = ''
	const numType = typeof value
	if (numType === 'number') {
		if (value === 0 || parseInt(value, 10) === value) {
			realVal = value
		} else {
			realVal = handleCutZero(parseFloat(value).toFixed(fixedNum))
		}
	} else if (numType === 'string' && !Number.isNaN(parseFloat(value))) {
		realVal = handleCutZero(parseFloat(value).toFixed(fixedNum))
	} else {
		return '-'
	}
	return Number(realVal)
}

/**
 * 将小数转化为百分比
 * @param {*} value 		目标数据
 * @param {*} fixedNum 	保留的小数位数
 * @returns	0.2 -> 20%
 */
export function keepNumPercent(value, fixedNum = 0) {
	let str = Number(value * 100).toFixed(fixedNum)
	str += '%'
	return str
}

/**
 * 为数字添加千位制分隔号
 * @param {*} num		目标数据
 * @returns	1000 -> 1,000
 */
export function thousandBitSeparator(num) {
	if (num) {
		const c =
			num.toString().indexOf('.') !== -1
				? num.toLocaleString()
				: num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
		return c
	}
}

/**
 * 换算 电能、金额 值和单位
 * @param {*} value 		目标数据
 * @param {*} basicUnit 基础单位
 * @returns value>=1w 返回 万kWh|万元; value<1w kWh|元
 * 备注：特变大屏，万元、万kWh，保留2位小数
 */
export function getDynamicValue(value, basicUnit) {
	let tempValue = parseFloat(value)
	if (!Number.isNaN(tempValue)) {
		if (basicUnit) {
			if (Math.abs(tempValue) >= 10000) {
				basicUnit = `万${basicUnit}`
			}
			return basicUnit
		}
		const isNegativeNum = tempValue < 0
		tempValue = Math.abs(tempValue)
		if (tempValue >= 10000) {
			tempValue = handleCutZero((tempValue / 10000).toFixed(2))
		} else {
			tempValue = handleCutZero(tempValue.toFixed(2))
		}
		return isNegativeNum ? `-${tempValue}` : tempValue
	}
	return basicUnit || value
}

/**
 * 去除小数点后多余的0
 * @param {*} num		目标数据
 * @returns	2.30 -> 2.3
 */
export function handleCutZero(num) {
	if (Number.isNaN(parseFloat(num))) {
		return '-'
	}
	num = `${num}`
	// 判断是否有效数
	if (num.indexOf('.') > -1) {
		// 拷贝一份 返回去掉零的新串
		let newstr = num
		// 循环变量 小数部分长度
		const leng = num.length - num.indexOf('.') - 1
		// 循环小数部分
		for (let i = leng; i > 0; i--) {
			// 如果newstr末尾有0
			if (
				newstr.lastIndexOf('0') > -1 &&
				newstr.substr(newstr.length - 1, 1) === '0'
			) {
				const k = newstr.lastIndexOf('0')
				// 如果小数点后只有一个0 去掉小数点
				if (newstr.charAt(k - 1) === '.') {
					return Number(newstr.substring(0, k - 1))
				}
				// 否则 去掉一个0
				newstr = newstr.substring(0, k)
			} else {
				// 如果末尾没有0
				return Number(newstr)
			}
		}
	}
	return Number(num)
}
